"""
Module để scrape danh sách hãng xe và model từ bonbanh.com
"""

from scraper_base import BonbanhScraper
from config import *
import re

class BrandScraper(BonbanhScraper):
    """Class để scrape danh sách hãng xe và model"""
    
    def __init__(self):
        super().__init__()
        self.brands = {}
        
    def get_all_brands(self):
        """
        Lấy danh sách tất cả hãng xe từ trang chủ
        """
        self.logger.info("Bắt đầu thu thập danh sách hãng xe...")
        
        response = self.get_page(BASE_URL)
        soup = self.parse_html(response)
        
        if not soup:
            self.logger.error("Không thể truy cập trang chủ")
            return {}
            
        brands = {}
        
        # Tìm section chứa danh sách hãng xe
        brand_section = soup.find('div', text=re.compile('Tìm theo hãng xe'))
        if not brand_section:
            # Th<PERSON> tìm bằng cách khác
            brand_links = soup.find_all('a', href=re.compile(r'/oto/[a-zA-Z_]+$'))
        else:
            brand_links = brand_section.find_next('ul').find_all('a') if brand_section.find_next('ul') else []
            
        # Nếu không tìm thấy, thử scrape từ trang /oto
        if not brand_links:
            self.logger.info("Không tìm thấy brands ở trang chủ, thử trang /oto")
            brands = self.get_brands_from_cars_page()
            
        for link in brand_links:
            href = link.get('href', '')
            brand_name = link.get_text(strip=True)
            
            # Extract brand slug từ URL
            match = re.search(r'/oto/([a-zA-Z_]+)$', href)
            if match:
                brand_slug = match.group(1)
                brands[brand_slug] = {
                    'name': brand_name,
                    'url': self.build_url(href),
                    'models': {}
                }
                
        self.logger.info(f"Tìm thấy {len(brands)} hãng xe")
        self.brands = brands
        return brands
        
    def get_brands_from_cars_page(self):
        """
        Lấy danh sách hãng xe từ trang /oto
        """
        response = self.get_page(CARS_URL)
        soup = self.parse_html(response)
        
        if not soup:
            return {}
            
        brands = {}
        
        # Tìm dropdown hoặc list chứa hãng xe
        brand_elements = soup.find_all('a', href=re.compile(r'/oto/[a-zA-Z_]+$'))
        
        for element in brand_elements:
            href = element.get('href', '')
            brand_name = element.get_text(strip=True)
            
            if not brand_name or len(brand_name) > 50:  # Lọc tên quá dài
                continue
                
            match = re.search(r'/oto/([a-zA-Z_]+)$', href)
            if match:
                brand_slug = match.group(1)
                brands[brand_slug] = {
                    'name': brand_name,
                    'url': self.build_url(href),
                    'models': {}
                }
                
        return brands
        
    def get_models_for_brand(self, brand_slug):
        """
        Lấy danh sách model cho một hãng xe cụ thể
        """
        if brand_slug not in self.brands:
            self.logger.warning(f"Không tìm thấy brand: {brand_slug}")
            return {}
            
        brand_url = self.brands[brand_slug]['url']
        self.logger.info(f"Đang thu thập models cho {brand_slug}...")
        
        response = self.get_page(brand_url)
        soup = self.parse_html(response)
        
        if not soup:
            return {}
            
        models = {}
        
        # Tìm danh sách models trong sidebar hoặc dropdown
        model_links = soup.find_all('a', href=re.compile(f'/oto/{brand_slug}-[a-zA-Z0-9_-]+$'))
        
        for link in model_links:
            href = link.get('href', '')
            model_name = link.get_text(strip=True)
            
            # Extract model slug
            match = re.search(f'/oto/{brand_slug}-([a-zA-Z0-9_-]+)$', href)
            if match:
                model_slug = match.group(1)
                models[model_slug] = {
                    'name': model_name,
                    'url': self.build_url(href),
                    'full_slug': f"{brand_slug}-{model_slug}"
                }
                
        self.logger.info(f"Tìm thấy {len(models)} models cho {brand_slug}")
        self.brands[brand_slug]['models'] = models
        return models
        
    def get_all_models(self):
        """
        Lấy tất cả models cho tất cả brands
        """
        if not self.brands:
            self.get_all_brands()
            
        for brand_slug in self.brands:
            self.get_models_for_brand(brand_slug)
            
        return self.brands
        
    def get_priority_brands(self):
        """
        Lấy danh sách các hãng xe ưu tiên
        """
        if not self.brands:
            self.get_all_brands()
            
        priority_brands = {}
        for brand_slug in PRIORITY_BRANDS:
            if brand_slug in self.brands:
                priority_brands[brand_slug] = self.brands[brand_slug]
                
        return priority_brands
