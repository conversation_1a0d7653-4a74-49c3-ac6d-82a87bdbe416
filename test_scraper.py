#!/usr/bin/env python3
"""
Test script để kiểm tra các components của scraper
"""

import sys
import logging
from brand_scraper import BrandScraper
from car_scraper import CarScraper
from database import CarDatabase
from price_analyzer import PriceAnalyzer

def test_brand_scraper():
    """Test BrandScraper"""
    print("=== TESTING BRAND SCRAPER ===")
    
    scraper = BrandScraper()
    
    # Test lấy brands
    print("Đang lấy danh sách brands...")
    brands = scraper.get_all_brands()
    
    if brands:
        print(f"✓ Tìm thấy {len(brands)} brands")
        for slug, data in list(brands.items())[:5]:  # Hiển thị 5 brands đầu
            print(f"  - {slug}: {data['name']}")
    else:
        print("✗ Không tìm thấy brands")
        return False
        
    # Test lấy models cho 1 brand
    if brands:
        test_brand = list(brands.keys())[0]
        print(f"\nĐang lấy models cho {test_brand}...")
        models = scraper.get_models_for_brand(test_brand)
        
        if models:
            print(f"✓ Tìm thấy {len(models)} models cho {test_brand}")
            for slug, data in list(models.items())[:3]:  # Hiển thị 3 models đầu
                print(f"  - {slug}: {data['name']}")
        else:
            print(f"✗ Không tìm thấy models cho {test_brand}")
            
    return True

def test_car_scraper():
    """Test CarScraper"""
    print("\n=== TESTING CAR SCRAPER ===")
    
    scraper = CarScraper()
    
    # Test với URL cụ thể
    test_url = "https://bonbanh.com/xe-audi-q7-2.0-at-2016-6341339"
    print(f"Đang test scrape car detail: {test_url}")
    
    car_data = scraper.scrape_car_detail(test_url)
    
    if car_data:
        print("✓ Scrape car detail thành công")
        print(f"  - Title: {car_data.get('title', 'N/A')}")
        print(f"  - Brand: {car_data.get('brand', 'N/A')}")
        print(f"  - Model: {car_data.get('model', 'N/A')}")
        print(f"  - Year: {car_data.get('year', 'N/A')}")
        print(f"  - Price: {car_data.get('price', 'N/A')}")
        print(f"  - Price Text: {car_data.get('price_text', 'N/A')}")
        return True
    else:
        print("✗ Không thể scrape car detail")
        return False

def test_database():
    """Test Database"""
    print("\n=== TESTING DATABASE ===")
    
    db = CarDatabase()
    
    # Test save car data
    test_car = {
        'listing_id': 'test123',
        'url': 'https://test.com',
        'title': 'Test Car',
        'brand': 'Toyota',
        'model': 'Camry',
        'year': 2020,
        'price': 500000000,
        'price_text': '500 Triệu',
        'condition': 'Xe cũ',
        'images': ['image1.jpg', 'image2.jpg']
    }
    
    if db.save_car(test_car):
        print("✓ Lưu car data thành công")
    else:
        print("✗ Không thể lưu car data")
        return False
        
    # Test get count
    count = db.get_car_count()
    print(f"✓ Số xe trong database: {count}")
    
    return True

def test_price_analyzer():
    """Test PriceAnalyzer"""
    print("\n=== TESTING PRICE ANALYZER ===")
    
    analyzer = PriceAnalyzer()
    
    # Test calculate average prices
    print("Đang tính toán giá trung bình...")
    results = analyzer.calculate_average_prices()
    
    if results:
        print("✓ Tính toán giá trung bình thành công")
        if 'summary' in results:
            summary = results['summary']
            print(f"  - Tổng xe: {summary.get('total_cars', 0)}")
            print(f"  - Tổng brands: {summary.get('total_brands', 0)}")
            print(f"  - Giá trung bình: {summary.get('overall_average_price', 0):,} VND")
        return True
    else:
        print("✗ Không thể tính toán giá trung bình")
        return False

def main():
    """Chạy tất cả tests"""
    print("BONBANH SCRAPER - RUNNING TESTS")
    print("=" * 50)
    
    # Setup logging
    logging.basicConfig(level=logging.INFO)
    
    tests = [
        ("Brand Scraper", test_brand_scraper),
        ("Car Scraper", test_car_scraper),
        ("Database", test_database),
        ("Price Analyzer", test_price_analyzer)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ Lỗi trong {test_name}: {e}")
            results.append((test_name, False))
    
    # Tổng kết
    print("\n" + "=" * 50)
    print("KẾT QUẢ TESTS:")
    
    passed = 0
    for test_name, result in results:
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
            
    print(f"\nTổng kết: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        print("🎉 Tất cả tests đều PASS!")
        return 0
    else:
        print("❌ Một số tests FAIL!")
        return 1

if __name__ == '__main__':
    sys.exit(main())
