#!/usr/bin/env python3
"""
Test script để kiểm tra save_brand và save_model với MySQL
"""

import sys
import logging
from database import CarDatabase


def test_mysql_brands_models():
    """Test save_brand và save_model với MySQL"""

    print("🧪 TESTING MYSQL BRANDS & MODELS")
    print("=" * 50)

    # Setup logging
    logging.basicConfig(level=logging.INFO)

    # Khởi tạo database với MySQL
    db = CarDatabase(use_mysql=True)

    # Test data
    test_brands = {
        'toyota': {
            'name': 'Toyota',
            'url': 'https://bonbanh.com/oto/toyota'
        },
        'honda': {
            'name': 'Honda',
            'url': 'https://bonbanh.com/oto/honda'
        },
        'mazda': {
            'name': 'Mazda',
            'url': 'https://bonbanh.com/oto/mazda'
        }
    }

    test_models = {
        'toyota': {
            'camry': {
                'name': '<PERSON><PERSON>',
                'url': 'https://bonbanh.com/oto/toyota-camry',
                'full_slug': 'toyota-camry'
            },
            'corolla': {
                'name': 'Corolla',
                'url': 'https://bonbanh.com/oto/toyota-corolla',
                'full_slug': 'toyota-corolla'
            }
        },
        'honda': {
            'civic': {
                'name': 'Civic',
                'url': 'https://bonbanh.com/oto/honda-civic',
                'full_slug': 'honda-civic'
            },
            'accord': {
                'name': 'Accord',
                'url': 'https://bonbanh.com/oto/honda-accord',
                'full_slug': 'honda-accord'
            }
        }
    }

    print("1. Testing save_brand()...")
    brand_success = 0
    brand_total = len(test_brands)

    for brand_slug, brand_data in test_brands.items():
        print(f"   Saving brand: {brand_slug}")
        if db.save_brand(brand_slug, brand_data):
            brand_success += 1
            print(f"   ✓ {brand_slug} saved successfully")
        else:
            print(f"   ✗ Failed to save {brand_slug}")

    print(f"\nBrand Results: {brand_success}/{brand_total} successful")

    print("\n2. Testing save_model()...")
    model_success = 0
    model_total = 0

    for brand_slug, models in test_models.items():
        for model_slug, model_data in models.items():
            model_total += 1
            print(f"   Saving model: {brand_slug}-{model_slug}")
            if db.save_model(brand_slug, model_slug, model_data):
                model_success += 1
                print(f"   ✓ {brand_slug}-{model_slug} saved successfully")
            else:
                print(f"   ✗ Failed to save {brand_slug}-{model_slug}")

    print(f"\nModel Results: {model_success}/{model_total} successful")

    print("\n3. Testing duplicate handling (UPSERT)...")
    # Test update existing brand
    updated_brand_data = {
        'name': 'Toyota Motor Corporation',  # Updated name
        'url': 'https://bonbanh.com/oto/toyota'
    }

    if db.save_brand('toyota', updated_brand_data):
        print("   ✓ Brand update (UPSERT) successful")
    else:
        print("   ✗ Brand update (UPSERT) failed")

    # Test update existing model
    updated_model_data = {
        'name': 'Camry Hybrid',  # Updated name
        'url': 'https://bonbanh.com/oto/toyota-camry-hybrid',
        'full_slug': 'toyota-camry-hybrid'
    }

    if db.save_model('toyota', 'camry', updated_model_data):
        print("   ✓ Model update (UPSERT) successful")
    else:
        print("   ✗ Model update (UPSERT) failed")

    print("\n4. Verifying data in database...")
    try:
        import mysql.connector
        import os
        from dotenv import load_dotenv

        load_dotenv()

        mysql_config = {
            'host': os.getenv('DB_HOST', '127.0.0.1'),
            'port': int(os.getenv('DB_PORT', 3306)),
            'database': os.getenv('DB_DATABASE', 'xehoi_pro'),
            'user': os.getenv('DB_USERNAME', 'root'),
            'password': os.getenv('DB_PASSWORD', 'pwdpwd')
        }

        connection = mysql.connector.connect(**mysql_config)
        cursor = connection.cursor()

        # Check brands
        cursor.execute("SELECT COUNT(*) FROM brands")
        brand_count = cursor.fetchone()[0]
        print(f"   Brands in database: {brand_count}")

        cursor.execute("SELECT slug, name FROM brands ORDER BY slug")
        brands = cursor.fetchall()
        for slug, name in brands:
            print(f"     - {slug}: {name}")

        # Check models
        cursor.execute("SELECT COUNT(*) FROM models")
        model_count = cursor.fetchone()[0]
        print(f"   Models in database: {model_count}")

        cursor.execute("SELECT brand_slug, slug, name FROM models ORDER BY brand_slug, slug")
        models = cursor.fetchall()
        for brand_slug, slug, name in models:
            print(f"     - {brand_slug}-{slug}: {name}")

        connection.close()

    except Exception as e:
        print(f"   ✗ Error verifying data: {e}")

    # Summary
    total_success = brand_success + model_success
    total_operations = brand_total + model_total

    print("\n" + "=" * 50)
    print("SUMMARY:")
    print(f"  Total operations: {total_operations}")
    print(f"  Successful: {total_success}")
    print(f"  Failed: {total_operations - total_success}")

    if total_success == total_operations:
        print("🎉 ALL TESTS PASSED!")
        return True
    else:
        print("❌ SOME TESTS FAILED!")
        return False


if __name__ == '__main__':
    success = test_mysql_brands_models()
    sys.exit(0 if success else 1)
