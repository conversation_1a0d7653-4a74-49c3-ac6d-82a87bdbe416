#!/usr/bin/env python3
"""
Demo script để chạy full scraping với nhiều brands
"""

import sys
import time
from main import BonbanhMainScraper

def demo_full_scraping():
    """Demo full scraping với nhiều brands"""
    
    print("🚗 BONBANH CAR SCRAPER - DEMO FULL SCRAPING")
    print("=" * 60)
    
    # Khởi tạo scraper với MySQL
    scraper = BonbanhMainScraper(use_mysql=True)
    
    # Danh sách brands để demo (có thể thay đổi)
    demo_brands = [
        'toyota',
        'honda', 
        'mazda',
        'hyundai',
        'kia'
    ]
    
    print(f"📋 Sẽ scrape các brands: {', '.join(demo_brands)}")
    print(f"🔢 Giới hạn: 20 xe mỗi brand để demo")
    print()
    
    # Hỏi user có muốn tiếp tục không
    response = input("Bạn có muốn tiếp tục? (y/N): ").lower().strip()
    if response != 'y':
        print("❌ Hủy bỏ scraping")
        return False
    
    print("\n🚀 Bắt đầu scraping...")
    start_time = time.time()
    
    try:
        # Chạy scraping
        success = scraper.run_full_scrape(
            brands=demo_brands,
            max_cars_per_brand=20  # Giới hạn để demo
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        if success:
            print(f"\n✅ SCRAPING HOÀN THÀNH!")
            print(f"⏱️  Thời gian: {duration:.1f} giây")
            
            # Hiển thị thống kê
            car_count = scraper.database.get_car_count()
            print(f"📊 Tổng số xe trong database: {car_count}")
            
            print("\n📁 Files được tạo:")
            print("  - bonbanh_cars_data.csv")
            print("  - bonbanh_cars_data.json") 
            print("  - price_summary.json")
            print("  - scraper.log")
            
            return True
        else:
            print("❌ Scraping thất bại!")
            return False
            
    except KeyboardInterrupt:
        print("\n⚠️  Scraping bị dừng bởi user")
        return False
    except Exception as e:
        print(f"\n❌ Lỗi: {e}")
        return False

def demo_quick_analysis():
    """Demo phân tích nhanh dữ liệu có sẵn"""
    
    print("\n📊 PHÂN TÍCH DỮ LIỆU")
    print("=" * 30)
    
    scraper = BonbanhMainScraper(use_mysql=True)
    
    # Lấy số lượng xe
    car_count = scraper.database.get_car_count()
    print(f"Tổng số xe: {car_count}")
    
    if car_count > 0:
        # Chạy phân tích
        scraper.analyze_prices()
        print("✅ Phân tích hoàn thành!")
        print("📄 Xem file price_summary.json để biết chi tiết")
    else:
        print("⚠️  Chưa có dữ liệu để phân tích")

def main():
    """Main function"""
    
    print("Chọn chế độ:")
    print("1. Demo Full Scraping (scrape nhiều brands)")
    print("2. Chỉ phân tích dữ liệu có sẵn")
    print("3. Thoát")
    
    choice = input("\nNhập lựa chọn (1-3): ").strip()
    
    if choice == '1':
        success = demo_full_scraping()
        if success:
            demo_quick_analysis()
    elif choice == '2':
        demo_quick_analysis()
    elif choice == '3':
        print("👋 Tạm biệt!")
    else:
        print("❌ Lựa chọn không hợp lệ")
        return 1
        
    return 0

if __name__ == '__main__':
    sys.exit(main())
