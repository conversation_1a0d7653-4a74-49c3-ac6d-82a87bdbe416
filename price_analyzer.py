"""
Module phân tích giá và tính toán giá trung bình
"""

import sqlite3
import mysql.connector
from mysql.connector import Error
import pandas as pd
import json
from collections import defaultdict
from config import *
import logging
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


class PriceAnalyzer:
    """Class phân tích giá xe và tính toán thống kê"""

    def __init__(self, use_mysql=True):
        self.use_mysql = use_mysql
        self.logger = logging.getLogger(__name__)

        if use_mysql:
            self.mysql_config = {
                'host': os.getenv('DB_HOST', '127.0.0.1'),
                'port': int(os.getenv('DB_PORT', 3306)),
                'database': os.getenv('DB_DATABASE', 'xehoi_pro'),
                'user': os.getenv('DB_USERNAME', 'root'),
                'password': os.getenv('DB_PASSWORD', 'pwdpwd'),
                'charset': 'utf8mb4',
                'collation': 'utf8mb4_unicode_ci'
            }
        else:
            self.db_name = DATABASE_NAME

    def calculate_average_prices(self):
        """
        Tính giá trung bình theo brand, model và năm
        """
        try:
            if self.use_mysql:
                connection = mysql.connector.connect(**self.mysql_config)
                # Lấy dữ liệu xe có giá hợp lệ
                df = pd.read_sql_query('''
                    SELECT brand, model, year, price, title, url
                    FROM cars
                    WHERE price IS NOT NULL AND price > 0
                    ORDER BY brand, model, year DESC
                ''', connection)
                connection.close()
            else:
                with sqlite3.connect(self.db_name) as conn:
                    # Lấy dữ liệu xe có giá hợp lệ
                    df = pd.read_sql_query('''
                        SELECT brand, model, year, price, title, url
                        FROM cars
                        WHERE price IS NOT NULL AND price > 0
                        ORDER BY brand, model, year DESC
                    ''', conn)

            if df.empty:
                self.logger.warning("Không có dữ liệu giá để phân tích")
                return {}

            # Tính toán thống kê theo nhóm
            results = {}

            # 1. Theo brand
            brand_stats = df.groupby('brand')['price'].agg([
                'count', 'mean', 'median', 'min', 'max', 'std'
            ]).round(0)

            results['by_brand'] = {}
            for brand in brand_stats.index:
                results['by_brand'][brand] = {
                    'count': int(brand_stats.loc[brand, 'count']),
                    'average_price': int(brand_stats.loc[brand, 'mean']),
                    'median_price': int(brand_stats.loc[brand, 'median']),
                    'min_price': int(brand_stats.loc[brand, 'min']),
                    'max_price': int(brand_stats.loc[brand, 'max']),
                    'std_price': int(brand_stats.loc[brand, 'std']) if pd.notna(brand_stats.loc[brand, 'std']) else 0
                }

            # 2. Theo brand + model
            model_stats = df.groupby(['brand', 'model'])['price'].agg([
                'count', 'mean', 'median', 'min', 'max', 'std'
            ]).round(0)

            results['by_model'] = {}
            for (brand, model) in model_stats.index:
                key = f"{brand}_{model}"
                results['by_model'][key] = {
                    'brand': brand,
                    'model': model,
                    'count': int(model_stats.loc[(brand, model), 'count']),
                    'average_price': int(model_stats.loc[(brand, model), 'mean']),
                    'median_price': int(model_stats.loc[(brand, model), 'median']),
                    'min_price': int(model_stats.loc[(brand, model), 'min']),
                    'max_price': int(model_stats.loc[(brand, model), 'max']),
                    'std_price': int(model_stats.loc[(brand, model), 'std']) if pd.notna(model_stats.loc[(brand, model), 'std']) else 0
                }

            # 3. Theo brand + model + year
            year_stats = df.groupby(['brand', 'model', 'year'])['price'].agg([
                'count', 'mean', 'median', 'min', 'max', 'std'
            ]).round(0)

            results['by_year'] = {}
            for (brand, model, year) in year_stats.index:
                key = f"{brand}_{model}_{year}"
                results['by_year'][key] = {
                    'brand': brand,
                    'model': model,
                    'year': int(year),
                    'count': int(year_stats.loc[(brand, model, year), 'count']),
                    'average_price': int(year_stats.loc[(brand, model, year), 'mean']),
                    'median_price': int(year_stats.loc[(brand, model, year), 'median']),
                    'min_price': int(year_stats.loc[(brand, model, year), 'min']),
                    'max_price': int(year_stats.loc[(brand, model, year), 'max']),
                    'std_price': int(year_stats.loc[(brand, model, year), 'std']) if pd.notna(year_stats.loc[(brand, model, year), 'std']) else 0
                }

            # Thống kê tổng quan
            results['summary'] = {
                'total_cars': len(df),
                'total_brands': df['brand'].nunique(),
                'total_models': df.groupby(['brand', 'model']).ngroups,
                'overall_average_price': int(df['price'].mean()),
                'overall_median_price': int(df['price'].median()),
                'price_range': {
                    'min': int(df['price'].min()),
                    'max': int(df['price'].max())
                }
            }

            self.logger.info(f"Đã tính toán giá trung bình cho {len(df)} xe")
            return results

        except Exception as e:
            self.logger.error(f"Lỗi khi tính toán giá trung bình: {e}")
            return {}

    def get_price_trends_by_year(self, brand=None, model=None):
        """
        Phân tích xu hướng giá theo năm
        """
        try:
            if self.use_mysql:
                connection = mysql.connector.connect(**self.mysql_config)
                query = '''
                    SELECT brand, model, year, AVG(price) as avg_price, COUNT(*) as count
                    FROM cars
                    WHERE price IS NOT NULL AND price > 0
                '''
                params = []

                if brand:
                    query += ' AND brand = %s'
                    params.append(brand)

                if model:
                    query += ' AND model = %s'
                    params.append(model)

                query += ' GROUP BY brand, model, year ORDER BY brand, model, year DESC'

                df = pd.read_sql_query(query, connection, params=params)
                connection.close()
            else:
                with sqlite3.connect(self.db_name) as conn:
                    query = '''
                        SELECT brand, model, year, AVG(price) as avg_price, COUNT(*) as count
                        FROM cars
                        WHERE price IS NOT NULL AND price > 0
                    '''
                    params = []

                    if brand:
                        query += ' AND brand = ?'
                        params.append(brand)

                    if model:
                        query += ' AND model = ?'
                        params.append(model)

                    query += ' GROUP BY brand, model, year ORDER BY brand, model, year DESC'

                    df = pd.read_sql_query(query, conn, params=params)

            if df.empty:
                return {}

            trends = {}
            for (brand, model), group in df.groupby(['brand', 'model']):
                key = f"{brand}_{model}"
                trends[key] = {
                    'brand': brand,
                    'model': model,
                    'years': []
                }

                for _, row in group.iterrows():
                    trends[key]['years'].append({
                        'year': int(row['year']),
                        'average_price': int(row['avg_price']),
                        'count': int(row['count'])
                    })

            return trends

        except Exception as e:
            self.logger.error(f"Lỗi khi phân tích xu hướng giá: {e}")
            return {}

    def find_best_deals(self, limit=20):
        """
        Tìm những xe có giá tốt nhất (dưới giá trung bình)
        """
        try:
            if self.use_mysql:
                connection = mysql.connector.connect(**self.mysql_config)
                df = pd.read_sql_query('''
                    SELECT c.*, avg_prices.model_avg_price
                    FROM cars c
                    JOIN (
                        SELECT brand, model, AVG(price) as model_avg_price
                        FROM cars
                        WHERE price IS NOT NULL AND price > 0
                        GROUP BY brand, model
                    ) avg_prices ON c.brand = avg_prices.brand AND c.model = avg_prices.model
                    WHERE c.price IS NOT NULL AND c.price > 0
                    AND c.price < avg_prices.model_avg_price * 0.9
                    ORDER BY (avg_prices.model_avg_price - c.price) DESC
                    LIMIT %s
                ''', connection, params=[limit])
                connection.close()
            else:
                with sqlite3.connect(self.db_name) as conn:
                    df = pd.read_sql_query('''
                        SELECT c.*, avg_prices.model_avg_price
                        FROM cars c
                        JOIN (
                            SELECT brand, model, AVG(price) as model_avg_price
                            FROM cars
                            WHERE price IS NOT NULL AND price > 0
                            GROUP BY brand, model
                        ) avg_prices ON c.brand = avg_prices.brand AND c.model = avg_prices.model
                        WHERE c.price IS NOT NULL AND c.price > 0
                        AND c.price < avg_prices.model_avg_price * 0.9
                        ORDER BY (avg_prices.model_avg_price - c.price) DESC
                        LIMIT ?
                    ''', conn, params=[limit])

            deals = []
            for _, row in df.iterrows():
                deals.append({
                    'listing_id': row['listing_id'],
                    'title': row['title'],
                    'brand': row['brand'],
                    'model': row['model'],
                    'year': int(row['year']) if pd.notna(row['year']) else None,
                    'price': int(row['price']),
                    'model_avg_price': int(row['model_avg_price']),
                    'savings': int(row['model_avg_price'] - row['price']),
                    'discount_percent': round((row['model_avg_price'] - row['price']) / row['model_avg_price'] * 100, 1),
                    'url': row['url']
                })

            return deals

        except Exception as e:
            self.logger.error(f"Lỗi khi tìm best deals: {e}")
            return []

    def save_price_analysis_to_db(self):
        """
        Lưu kết quả phân tích giá vào database
        """
        try:
            # Lấy kết quả phân tích
            analysis_results = self.calculate_average_prices()

            if not analysis_results:
                self.logger.warning("Không có dữ liệu phân tích để lưu")
                return False

            if self.use_mysql:
                return self._save_analysis_mysql(analysis_results)
            else:
                return self._save_analysis_sqlite(analysis_results)

        except Exception as e:
            self.logger.error(f"Lỗi khi lưu phân tích giá vào database: {e}")
            return False

    def _save_analysis_mysql(self, analysis_results):
        """Lưu phân tích vào MySQL"""
        try:
            connection = mysql.connector.connect(**self.mysql_config)
            cursor = connection.cursor()

            # Xóa dữ liệu cũ
            cursor.execute("DELETE FROM price_analysis")

            # Lưu phân tích theo brand
            if 'by_brand' in analysis_results:
                for brand, stats in analysis_results['by_brand'].items():
                    cursor.execute('''
                        INSERT INTO price_analysis
                        (analysis_type, brand, count, avg_price, median_price, min_price, max_price, std_price)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                    ''', (
                        'brand', brand, stats['count'], stats['average_price'],
                        stats['median_price'], stats['min_price'], stats['max_price'], stats['std_price']
                    ))

            # Lưu phân tích theo model
            if 'by_model' in analysis_results:
                for model_key, stats in analysis_results['by_model'].items():
                    cursor.execute('''
                        INSERT INTO price_analysis
                        (analysis_type, brand, model, count, avg_price, median_price, min_price, max_price, std_price)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                    ''', (
                        'model', stats['brand'], stats['model'], stats['count'], stats['average_price'],
                        stats['median_price'], stats['min_price'], stats['max_price'], stats['std_price']
                    ))

            # Lưu phân tích theo year
            if 'by_year' in analysis_results:
                for year_key, stats in analysis_results['by_year'].items():
                    cursor.execute('''
                        INSERT INTO price_analysis
                        (analysis_type, brand, model, year, count, avg_price, median_price, min_price, max_price, std_price)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    ''', (
                        'year', stats['brand'], stats['model'], stats['year'], stats['count'], stats['average_price'],
                        stats['median_price'], stats['min_price'], stats['max_price'], stats['std_price']
                    ))

            connection.commit()
            self.logger.info("Đã lưu phân tích giá vào MySQL database")
            return True

        except Error as e:
            self.logger.error(f"Lỗi khi lưu phân tích vào MySQL: {e}")
            return False
        finally:
            if connection.is_connected():
                cursor.close()
                connection.close()

    def _save_analysis_sqlite(self, analysis_results):
        """Lưu phân tích vào SQLite (chưa implement bảng price_analysis cho SQLite)"""
        self.logger.info("SQLite price_analysis table chưa được implement")
        return True

    def export_price_analysis(self, filename=SUMMARY_OUTPUT, save_to_db=True):
        """
        Export kết quả phân tích giá ra file JSON và lưu vào database
        """
        try:
            analysis = {
                'average_prices': self.calculate_average_prices(),
                'price_trends': self.get_price_trends_by_year(),
                'best_deals': self.find_best_deals(),
                'generated_at': pd.Timestamp.now().isoformat()
            }

            # Export ra file JSON
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(analysis, f, ensure_ascii=False, indent=2, default=str)

            self.logger.info(f"Đã export phân tích giá ra {filename}")

            # Lưu vào database
            if save_to_db:
                self.save_price_analysis_to_db()

            return True

        except Exception as e:
            self.logger.error(f"Lỗi khi export phân tích giá: {e}")
            return False
