# Bonbanh Car Scraper

C<PERSON><PERSON> cụ web scraping để thu thập dữ liệu xe hơi cũ từ bonbanh.com - trang web mua bán ô tô uy tín tại Việt Nam.

## Tính năng

- 🚗 Thu thập thông tin chi tiết của các xe đăng bán
- 🏷️ Trích xuất thông tin: hãng xe, model, năm sản xuất, gi<PERSON> bán, thông số kỹ thuật
- 📊 Tính toán giá trung bình theo hãng xe, model và năm sản xuất
- 💾 Lưu trữ dữ liệu vào SQLite database
- 📁 Export dữ liệu ra CSV và JSON
- 🔍 Phân tích xu hướng giá và tìm những deal tốt nhất
- ⚡ Xử lý lỗi và rate limiting để tránh quá tải server

## Cài đặt

### Yêu cầu hệ thống
- Python 3.7+
- Kết nối internet

### Cài đặt dependencies

```bash
pip install -r requirements.txt
```

### Cấu trúc thư mục

```
bonbanh-scraper/
├── main.py              # Script chính
├── test_scraper.py      # Script test
├── config.py            # Cấu hình
├── scraper_base.py      # Base class cho scraping
├── brand_scraper.py     # Scraper cho brands và models
├── car_scraper.py       # Scraper cho chi tiết xe
├── database.py          # Quản lý database
├── price_analyzer.py    # Phân tích giá
├── requirements.txt     # Dependencies
└── README.md           # Hướng dẫn này
```

## Sử dụng

### 1. Chạy test để kiểm tra

```bash
python test_scraper.py
```

### 2. Chạy scraping mode test (thu thập ít dữ liệu để test)

```bash
python main.py --mode test
```

### 3. Chạy scraping đầy đủ

```bash
# Scrape tất cả brands ưu tiên
python main.py --mode full

# Scrape brands cụ thể
python main.py --mode full --brands toyota honda mazda

# Giới hạn số xe mỗi brand
python main.py --mode full --max-cars 50
```

### 4. Chỉ phân tích dữ liệu có sẵn

```bash
python main.py --mode analyze
```

## Dữ liệu thu thập

### Thông tin xe
- **Cơ bản**: Tiêu đề, hãng xe, model, năm sản xuất
- **Giá cả**: Giá bán (VND), text giá gốc
- **Thông số**: Tình trạng, km đã đi, loại nhiên liệu, hộp số
- **Ngoại hình**: Xuất xứ, màu ngoại thất, màu nội thất, số chỗ ngồi
- **Người bán**: Tên, số điện thoại, địa chỉ
- **Khác**: Mô tả, hình ảnh, URL

### Ví dụ dữ liệu

```json
{
  "listing_id": "6341339",
  "title": "Xe Audi Q7 2.0 AT 2016",
  "brand": "Audi",
  "model": "Q7 2.0 AT",
  "year": 2016,
  "price": 1188000000,
  "price_text": "1 Tỷ 188 Triệu",
  "condition": "Xe đã dùng",
  "km": 113000,
  "fuel_type": "Xăng 2.0 L",
  "transmission": "Số tự động"
}
```

## Phân tích giá

Công cụ tự động tính toán:

### 1. Giá trung bình theo hãng xe
```json
{
  "Toyota": {
    "count": 1250,
    "average_price": 650000000,
    "median_price": 580000000,
    "min_price": 150000000,
    "max_price": 2500000000
  }
}
```

### 2. Giá trung bình theo model
```json
{
  "Toyota_Camry": {
    "brand": "Toyota",
    "model": "Camry",
    "count": 85,
    "average_price": 850000000,
    "median_price": 800000000
  }
}
```

### 3. Giá trung bình theo năm sản xuất
```json
{
  "Toyota_Camry_2020": {
    "brand": "Toyota",
    "model": "Camry", 
    "year": 2020,
    "count": 12,
    "average_price": 1200000000
  }
}
```

## Files output

- `bonbanh_cars.db` - SQLite database chứa tất cả dữ liệu
- `bonbanh_cars_data.csv` - Dữ liệu xe dạng CSV
- `bonbanh_cars_data.json` - Dữ liệu xe dạng JSON
- `price_summary.json` - Phân tích giá chi tiết
- `scraper.log` - Log file

## Cấu hình

Chỉnh sửa `config.py` để:

- Thay đổi delay giữa các requests
- Cấu hình số trang tối đa mỗi brand
- Thêm/bớt brands ưu tiên
- Thay đổi tên file output

## Lưu ý quan trọng

### Tuân thủ robots.txt
- Công cụ có built-in delay để tránh quá tải server
- Không chạy nhiều instance cùng lúc
- Tôn trọng terms of service của bonbanh.com

### Xử lý lỗi
- Tự động retry khi gặp lỗi network
- Skip các URL không hợp lệ
- Log chi tiết để debug

### Performance
- Sử dụng session để tái sử dụng connection
- Batch insert vào database
- Có thể dừng và tiếp tục scraping

## Troubleshooting

### Lỗi thường gặp

1. **Connection timeout**
   - Kiểm tra kết nối internet
   - Tăng timeout trong config

2. **Không tìm thấy elements**
   - Website có thể đã thay đổi cấu trúc
   - Cần update selectors trong code

3. **Database locked**
   - Đóng tất cả connections đến database
   - Restart script

### Debug

Bật debug logging:
```python
# Trong config.py
LOG_LEVEL = "DEBUG"
```

## Đóng góp

1. Fork repository
2. Tạo feature branch
3. Commit changes
4. Push và tạo Pull Request

## License

MIT License - Xem file LICENSE để biết chi tiết.

## Disclaimer

Công cụ này chỉ dành cho mục đích nghiên cứu và học tập. Người dùng có trách nhiệm tuân thủ terms of service của bonbanh.com và các quy định pháp luật liên quan.
