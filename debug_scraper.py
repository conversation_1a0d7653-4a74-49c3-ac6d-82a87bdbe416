#!/usr/bin/env python3
"""
Debug script để kiểm tra scraping
"""

import requests
from bs4 import BeautifulSoup
import re

def debug_peugeot_page():
    """Debug trang Peugeot để tìm car listings"""
    
    url = "https://bonbanh.com/oto/peugeot"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    print(f"Đang truy cập: {url}")
    
    response = requests.get(url, headers=headers)
    soup = BeautifulSoup(response.text, 'lxml')
    
    print(f"Status code: {response.status_code}")
    print(f"Page title: {soup.title.get_text() if soup.title else 'No title'}")
    
    # Tìm tất cả links có chứa "xe-"
    all_xe_links = soup.find_all('a', href=re.compile(r'/xe-'))
    print(f"\nTìm thấy {len(all_xe_links)} links chứa '/xe-'")
    
    for i, link in enumerate(all_xe_links[:10]):  # Hiển thị 10 links đầu
        href = link.get('href', '')
        text = link.get_text(strip=True)
        print(f"  {i+1}. {href} - {text[:50]}...")
        
    # Tìm pattern cụ thể
    patterns = [
        r'/xe-[a-zA-Z0-9_-]+-\d{4}-\d+$',
        r'/xe-.+-\d+$',
        r'/xe-peugeot-.+-\d+',
        r'xe-.*-\d{4}-\d+'
    ]
    
    for pattern in patterns:
        matches = soup.find_all('a', href=re.compile(pattern))
        print(f"\nPattern '{pattern}': {len(matches)} matches")
        for match in matches[:3]:
            print(f"  - {match.get('href', '')}")
            
    # Tìm trong các div chứa listings
    listing_divs = soup.find_all('div', class_=re.compile(r'listing|item|car'))
    print(f"\nTìm thấy {len(listing_divs)} divs có class chứa 'listing/item/car'")
    
    # Tìm tất cả links trong trang
    all_links = soup.find_all('a', href=True)
    xe_links = [link for link in all_links if '/xe-' in link.get('href', '')]
    
    print(f"\nTổng số links chứa '/xe-': {len(xe_links)}")
    
    # Phân tích cấu trúc HTML
    print("\n=== PHÂN TÍCH CẤU TRÚC ===")
    
    # Tìm các thẻ có thể chứa car listings
    possible_containers = soup.find_all(['div', 'ul', 'li'], class_=True)
    
    for container in possible_containers:
        classes = ' '.join(container.get('class', []))
        if any(keyword in classes.lower() for keyword in ['car', 'item', 'listing', 'product']):
            links_in_container = container.find_all('a', href=re.compile(r'/xe-'))
            if links_in_container:
                print(f"Container với class '{classes}': {len(links_in_container)} xe links")

if __name__ == '__main__':
    debug_peugeot_page()
