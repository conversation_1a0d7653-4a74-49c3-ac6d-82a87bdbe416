# MySQL Setup cho Bonbanh Car Scraper

## Yêu cầu
- MySQL Server 5.7+ hoặc 8.0+
- Python 3.7+
- Quyền tạo database và tables

## Cài đặt nhanh

### 1. Cấu hình database
Chỉnh sửa file `.env`:
```env
DB_HOST=127.0.0.1
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=your_password_here
DB_DATABASE=xehoi_pro
```

### 2. Cài đặt dependencies
```bash
pip install -r requirements.txt
```

### 3. Tạo database và tables
```bash
python create_mysql_tables.py
```

### 4. Test setup
```bash
python test_scraper.py
```

## Sử dụng

### Scraping cơ bản
```bash
# Test với ít dữ liệu
python main.py --mode test

# Full scraping
python main.py --mode full

# Scrape brands cụ thể
python main.py --mode full --brands toyota honda mazda
```

### Demo scraping
```bash
python demo_full_scraping.py
```

### Chỉ phân tích dữ liệu
```bash
python main.py --mode analyze
```

## Database Schema

### Bảng `cars`
Chứa thông tin chi tiết từng xe:
- `listing_id` - ID unique
- `brand`, `model`, `year` - Thông tin xe
- `price` - Giá (VND)
- `condition`, `km`, `fuel_type` - Tình trạng
- `seller_*` - Thông tin người bán
- `images` - JSON array hình ảnh

### Bảng `brands` và `models`
Cấu trúc phân cấp brands → models

### Bảng `price_analysis`
Kết quả phân tích giá theo brand/model/year

## Output Files

- `bonbanh_cars_data.csv` - Dữ liệu xe dạng CSV
- `bonbanh_cars_data.json` - Dữ liệu xe dạng JSON  
- `price_summary.json` - Phân tích giá chi tiết
- `scraper.log` - Log file

## Troubleshooting

### Lỗi kết nối MySQL
1. Kiểm tra MySQL server đang chạy
2. Verify thông tin trong `.env`
3. Kiểm tra quyền user

### Lỗi encoding
Database sử dụng `utf8mb4` để hỗ trợ tiếng Việt đầy đủ

### Performance
- Sử dụng indexes để tối ưu query
- Batch operations cho large datasets
- Monitor memory usage khi scrape nhiều dữ liệu

## Queries hữu ích

```sql
-- Thống kê tổng quan
SELECT 
    brand,
    COUNT(*) as total_cars,
    AVG(price) as avg_price,
    MIN(price) as min_price,
    MAX(price) as max_price
FROM cars 
WHERE price > 0
GROUP BY brand
ORDER BY total_cars DESC;

-- Top 10 xe giá cao nhất
SELECT title, brand, model, year, price, url
FROM cars 
WHERE price > 0
ORDER BY price DESC 
LIMIT 10;

-- Xe theo năm sản xuất
SELECT 
    year,
    COUNT(*) as count,
    AVG(price) as avg_price
FROM cars 
WHERE year BETWEEN 2015 AND 2025
GROUP BY year
ORDER BY year DESC;
```
