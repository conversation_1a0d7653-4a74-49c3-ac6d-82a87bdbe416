#!/usr/bin/env python3
"""
Script kiểm tra dữ liệu trong MySQL database
"""

import mysql.connector
import os
from dotenv import load_dotenv

load_dotenv()

def check_mysql_data():
    """Kiểm tra dữ liệu trong MySQL"""
    
    print("📊 KIỂM TRA DỮ LIỆU MYSQL")
    print("=" * 50)
    
    try:
        mysql_config = {
            'host': os.getenv('DB_HOST', '127.0.0.1'),
            'port': int(os.getenv('DB_PORT', 3306)),
            'database': os.getenv('DB_DATABASE', 'xehoi_pro'),
            'user': os.getenv('DB_USERNAME', 'root'),
            'password': os.getenv('DB_PASSWORD', 'pwdpwd')
        }
        
        connection = mysql.connector.connect(**mysql_config)
        cursor = connection.cursor()
        
        # Check brands
        print("1. BRANDS:")
        cursor.execute("SELECT COUNT(*) FROM brands")
        brand_count = cursor.fetchone()[0]
        print(f"   Tổng số brands: {brand_count}")
        
        cursor.execute("SELECT slug, name, url FROM brands ORDER BY slug")
        brands = cursor.fetchall()
        for slug, name, url in brands:
            print(f"   - {slug}: {name}")
            print(f"     URL: {url}")
        
        print()
        
        # Check models
        print("2. MODELS:")
        cursor.execute("SELECT COUNT(*) FROM models")
        model_count = cursor.fetchone()[0]
        print(f"   Tổng số models: {model_count}")
        
        cursor.execute("""
            SELECT m.brand_slug, m.slug, m.name, m.url, m.full_slug 
            FROM models m 
            ORDER BY m.brand_slug, m.slug
        """)
        models = cursor.fetchall()
        
        current_brand = None
        for brand_slug, slug, name, url, full_slug in models:
            if brand_slug != current_brand:
                print(f"\n   Brand: {brand_slug}")
                current_brand = brand_slug
            print(f"     - {slug}: {name}")
            print(f"       URL: {url}")
            print(f"       Full slug: {full_slug}")
        
        print()
        
        # Check cars
        print("3. CARS:")
        cursor.execute("SELECT COUNT(*) FROM cars")
        car_count = cursor.fetchone()[0]
        print(f"   Tổng số cars: {car_count}")
        
        cursor.execute("""
            SELECT brand, model, year, COUNT(*) as count, AVG(price) as avg_price
            FROM cars 
            WHERE price > 0
            GROUP BY brand, model, year
            ORDER BY brand, model, year DESC
        """)
        car_stats = cursor.fetchall()
        
        current_brand = None
        for brand, model, year, count, avg_price in car_stats:
            if brand != current_brand:
                print(f"\n   Brand: {brand}")
                current_brand = brand
            print(f"     - {model} ({year}): {count} xe, giá TB: {avg_price:,.0f} VND")
        
        print()
        
        # Check recent cars
        print("4. RECENT CARS (Top 10):")
        cursor.execute("""
            SELECT listing_id, title, brand, model, year, price, url
            FROM cars 
            ORDER BY scraped_at DESC
            LIMIT 10
        """)
        recent_cars = cursor.fetchall()
        
        for listing_id, title, brand, model, year, price, url in recent_cars:
            print(f"   - {listing_id}: {title}")
            print(f"     {brand} {model} {year} - {price:,} VND")
            print(f"     {url}")
            print()
        
        connection.close()
        
        print("=" * 50)
        print("TỔNG KẾT:")
        print(f"  📁 Brands: {brand_count}")
        print(f"  🚗 Models: {model_count}")
        print(f"  🏷️  Cars: {car_count}")
        print("✅ Dữ liệu đã được lưu thành công vào MySQL!")
        
        return True
        
    except Exception as e:
        print(f"❌ Lỗi khi kiểm tra dữ liệu: {e}")
        return False

if __name__ == '__main__':
    check_mysql_data()
