"""
Module quản lý database và export dữ liệu
"""

import sqlite3
import mysql.connector
from mysql.connector import Error
import json
import csv
import pandas as pd
from datetime import datetime
from config import *
import logging
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


class CarDatabase:
    """Class quản lý database MySQL/SQLite cho dữ liệu xe"""

    def __init__(self, use_mysql=True):
        self.use_mysql = use_mysql
        self.logger = logging.getLogger(__name__)

        if use_mysql:
            self.mysql_config = {
                'host': os.getenv('DB_HOST', '127.0.0.1'),
                'port': int(os.getenv('DB_PORT', 3306)),
                'database': os.getenv('DB_DATABASE', 'xehoi_pro'),
                'user': os.getenv('DB_USERNAME', 'root'),
                'password': os.getenv('DB_PASSWORD', 'pwdpwd'),
                'charset': 'utf8mb4',
                'collation': 'utf8mb4_unicode_ci'
            }
        else:
            self.db_name = DATABASE_NAME

        self.init_database()

    def init_database(self):
        """Khởi tạo database và tạo tables"""
        if self.use_mysql:
            self._init_mysql_database()
        else:
            self._init_sqlite_database()

    def _init_mysql_database(self):
        """Khởi tạo MySQL database"""
        try:
            connection = mysql.connector.connect(**self.mysql_config)
            cursor = connection.cursor()

            # Tạo bảng cars
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS cars (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    listing_id VARCHAR(50) UNIQUE,
                    url TEXT,
                    title TEXT,
                    brand VARCHAR(100),
                    model VARCHAR(100),
                    year INT,
                    price BIGINT,
                    price_text VARCHAR(100),
                    `condition` VARCHAR(50),
                    km INT,
                    fuel_type VARCHAR(50),
                    transmission VARCHAR(50),
                    origin VARCHAR(50),
                    color_exterior VARCHAR(50),
                    color_interior VARCHAR(50),
                    seats INT,
                    doors INT,
                    drivetrain VARCHAR(50),
                    description TEXT,
                    seller_name VARCHAR(200),
                    seller_phone VARCHAR(50),
                    seller_address TEXT,
                    images JSON,
                    scraped_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ''')

            # Tạo bảng brands
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS brands (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    slug VARCHAR(100) UNIQUE,
                    name VARCHAR(200),
                    url TEXT,
                    scraped_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ''')

            # Tạo bảng models
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS models (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    brand_slug VARCHAR(100),
                    slug VARCHAR(100),
                    name VARCHAR(200),
                    url TEXT,
                    full_slug VARCHAR(200),
                    scraped_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (brand_slug) REFERENCES brands (slug) ON DELETE CASCADE
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ''')

            # Tạo indexes
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_cars_brand_model_year ON cars (brand, model, year)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_cars_price ON cars (price)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_cars_year ON cars (year)')

            connection.commit()
            self.logger.info("MySQL Database initialized successfully")

        except Error as e:
            self.logger.error(f"Error initializing MySQL database: {e}")
        finally:
            if connection.is_connected():
                cursor.close()
                connection.close()

    def _init_sqlite_database(self):
        """Khởi tạo SQLite database (fallback)"""
        try:
            with sqlite3.connect(self.db_name) as conn:
                cursor = conn.cursor()

                # Tạo bảng cars
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS cars (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        listing_id TEXT UNIQUE,
                        url TEXT,
                        title TEXT,
                        brand TEXT,
                        model TEXT,
                        year INTEGER,
                        price INTEGER,
                        price_text TEXT,
                        condition TEXT,
                        km INTEGER,
                        fuel_type TEXT,
                        transmission TEXT,
                        origin TEXT,
                        color_exterior TEXT,
                        color_interior TEXT,
                        seats INTEGER,
                        doors INTEGER,
                        drivetrain TEXT,
                        description TEXT,
                        seller_name TEXT,
                        seller_phone TEXT,
                        seller_address TEXT,
                        images TEXT,
                        scraped_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')

                # Tạo bảng brands
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS brands (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        slug TEXT UNIQUE,
                        name TEXT,
                        url TEXT,
                        scraped_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')

                # Tạo bảng models
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS models (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        brand_slug TEXT,
                        slug TEXT,
                        name TEXT,
                        url TEXT,
                        full_slug TEXT,
                        scraped_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (brand_slug) REFERENCES brands (slug)
                    )
                ''')

                # Tạo indexes
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_cars_brand_model_year ON cars (brand, model, year)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_cars_price ON cars (price)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_cars_year ON cars (year)')

                conn.commit()
                self.logger.info("SQLite Database initialized successfully")

        except Exception as e:
            self.logger.error(f"Error initializing SQLite database: {e}")

    def save_car(self, car_data):
        """Lưu thông tin một xe vào database"""
        if self.use_mysql:
            return self._save_car_mysql(car_data)
        else:
            return self._save_car_sqlite(car_data)

    def _save_car_mysql(self, car_data):
        """Lưu xe vào MySQL"""
        try:
            connection = mysql.connector.connect(**self.mysql_config)
            cursor = connection.cursor()

            # Convert images list to JSON string
            images_json = json.dumps(car_data.get('images', []))

            cursor.execute('''
                INSERT INTO cars (
                    listing_id, url, title, brand, model, year, price, price_text,
                    `condition`, km, fuel_type, transmission, origin, color_exterior,
                    color_interior, seats, doors, drivetrain, description,
                    seller_name, seller_phone, seller_address, images
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                ON DUPLICATE KEY UPDATE
                    url = VALUES(url),
                    title = VALUES(title),
                    brand = VALUES(brand),
                    model = VALUES(model),
                    year = VALUES(year),
                    price = VALUES(price),
                    price_text = VALUES(price_text),
                    `condition` = VALUES(`condition`),
                    km = VALUES(km),
                    fuel_type = VALUES(fuel_type),
                    transmission = VALUES(transmission),
                    origin = VALUES(origin),
                    color_exterior = VALUES(color_exterior),
                    color_interior = VALUES(color_interior),
                    seats = VALUES(seats),
                    doors = VALUES(doors),
                    drivetrain = VALUES(drivetrain),
                    description = VALUES(description),
                    seller_name = VALUES(seller_name),
                    seller_phone = VALUES(seller_phone),
                    seller_address = VALUES(seller_address),
                    images = VALUES(images)
            ''', (
                car_data.get('listing_id'),
                car_data.get('url'),
                car_data.get('title'),
                car_data.get('brand'),
                car_data.get('model'),
                car_data.get('year'),
                car_data.get('price'),
                car_data.get('price_text'),
                car_data.get('condition'),
                car_data.get('km'),
                car_data.get('fuel_type'),
                car_data.get('transmission'),
                car_data.get('origin'),
                car_data.get('color_exterior'),
                car_data.get('color_interior'),
                car_data.get('seats'),
                car_data.get('doors'),
                car_data.get('drivetrain'),
                car_data.get('description'),
                car_data.get('seller_name'),
                car_data.get('seller_phone'),
                car_data.get('seller_address'),
                images_json
            ))

            connection.commit()
            return True

        except Error as e:
            self.logger.error(f"Error saving car data to MySQL: {e}")
            return False
        finally:
            if connection.is_connected():
                cursor.close()
                connection.close()

    def _save_car_sqlite(self, car_data):
        """Lưu xe vào SQLite"""
        try:
            with sqlite3.connect(self.db_name) as conn:
                cursor = conn.cursor()

                # Convert images list to JSON string
                images_json = json.dumps(car_data.get('images', []))

                cursor.execute('''
                    INSERT OR REPLACE INTO cars (
                        listing_id, url, title, brand, model, year, price, price_text,
                        condition, km, fuel_type, transmission, origin, color_exterior,
                        color_interior, seats, doors, drivetrain, description,
                        seller_name, seller_phone, seller_address, images
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    car_data.get('listing_id'),
                    car_data.get('url'),
                    car_data.get('title'),
                    car_data.get('brand'),
                    car_data.get('model'),
                    car_data.get('year'),
                    car_data.get('price'),
                    car_data.get('price_text'),
                    car_data.get('condition'),
                    car_data.get('km'),
                    car_data.get('fuel_type'),
                    car_data.get('transmission'),
                    car_data.get('origin'),
                    car_data.get('color_exterior'),
                    car_data.get('color_interior'),
                    car_data.get('seats'),
                    car_data.get('doors'),
                    car_data.get('drivetrain'),
                    car_data.get('description'),
                    car_data.get('seller_name'),
                    car_data.get('seller_phone'),
                    car_data.get('seller_address'),
                    images_json
                ))

                conn.commit()
                return True

        except Exception as e:
            self.logger.error(f"Error saving car data to SQLite: {e}")
            return False

    def save_brand(self, brand_slug, brand_data):
        """Lưu thông tin hãng xe"""
        if self.use_mysql:
            return self._save_brand_mysql(brand_slug, brand_data)
        else:
            return self._save_brand_sqlite(brand_slug, brand_data)

    def _save_brand_mysql(self, brand_slug, brand_data):
        """Lưu brand vào MySQL"""
        try:
            connection = mysql.connector.connect(**self.mysql_config)
            cursor = connection.cursor()

            cursor.execute('''
                INSERT INTO brands (slug, name, url)
                VALUES (%s, %s, %s)
                ON DUPLICATE KEY UPDATE
                    name = VALUES(name),
                    url = VALUES(url)
            ''', (brand_slug, brand_data.get('name'), brand_data.get('url')))

            connection.commit()
            return True

        except Error as e:
            self.logger.error(f"Error saving brand data to MySQL: {e}")
            return False
        finally:
            if connection.is_connected():
                cursor.close()
                connection.close()

    def _save_brand_sqlite(self, brand_slug, brand_data):
        """Lưu brand vào SQLite"""
        try:
            with sqlite3.connect(self.db_name) as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    INSERT OR REPLACE INTO brands (slug, name, url)
                    VALUES (?, ?, ?)
                ''', (brand_slug, brand_data.get('name'), brand_data.get('url')))

                conn.commit()
                return True

        except Exception as e:
            self.logger.error(f"Error saving brand data to SQLite: {e}")
            return False

    def save_model(self, brand_slug, model_slug, model_data):
        """Lưu thông tin model xe"""
        if self.use_mysql:
            return self._save_model_mysql(brand_slug, model_slug, model_data)
        else:
            return self._save_model_sqlite(brand_slug, model_slug, model_data)

    def _save_model_mysql(self, brand_slug, model_slug, model_data):
        """Lưu model vào MySQL"""
        try:
            connection = mysql.connector.connect(**self.mysql_config)
            cursor = connection.cursor()

            cursor.execute('''
                INSERT INTO models (brand_slug, slug, name, url, full_slug)
                VALUES (%s, %s, %s, %s, %s)
                ON DUPLICATE KEY UPDATE
                    name = VALUES(name),
                    url = VALUES(url),
                    full_slug = VALUES(full_slug)
            ''', (
                brand_slug,
                model_slug,
                model_data.get('name'),
                model_data.get('url'),
                model_data.get('full_slug')
            ))

            connection.commit()
            return True

        except Error as e:
            self.logger.error(f"Error saving model data to MySQL: {e}")
            return False
        finally:
            if connection.is_connected():
                cursor.close()
                connection.close()

    def _save_model_sqlite(self, brand_slug, model_slug, model_data):
        """Lưu model vào SQLite"""
        try:
            with sqlite3.connect(self.db_name) as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    INSERT OR REPLACE INTO models (brand_slug, slug, name, url, full_slug)
                    VALUES (?, ?, ?, ?, ?)
                ''', (
                    brand_slug,
                    model_slug,
                    model_data.get('name'),
                    model_data.get('url'),
                    model_data.get('full_slug')
                ))

                conn.commit()
                return True

        except Exception as e:
            self.logger.error(f"Error saving model data to SQLite: {e}")
            return False

    def get_car_count(self):
        """Lấy tổng số xe trong database"""
        try:
            if self.use_mysql:
                connection = mysql.connector.connect(**self.mysql_config)
                cursor = connection.cursor()
                cursor.execute('SELECT COUNT(*) FROM cars')
                count = cursor.fetchone()[0]
                connection.close()
                return count
            else:
                with sqlite3.connect(self.db_name) as conn:
                    cursor = conn.cursor()
                    cursor.execute('SELECT COUNT(*) FROM cars')
                    return cursor.fetchone()[0]
        except Exception as e:
            self.logger.error(f"Error getting car count: {e}")
            return 0

    def export_to_csv(self, filename=CSV_OUTPUT):
        """Export dữ liệu ra file CSV"""
        try:
            if self.use_mysql:
                connection = mysql.connector.connect(**self.mysql_config)
                df = pd.read_sql_query('''
                    SELECT listing_id, title, brand, model, year, price, price_text,
                           `condition`, km, fuel_type, transmission, origin,
                           color_exterior, color_interior, seats, doors, drivetrain,
                           seller_name, seller_phone, seller_address, url
                    FROM cars
                    ORDER BY brand, model, year DESC
                ''', connection)
                connection.close()
            else:
                with sqlite3.connect(self.db_name) as conn:
                    df = pd.read_sql_query('''
                        SELECT listing_id, title, brand, model, year, price, price_text,
                               condition, km, fuel_type, transmission, origin,
                               color_exterior, color_interior, seats, doors, drivetrain,
                               seller_name, seller_phone, seller_address, url
                        FROM cars
                        ORDER BY brand, model, year DESC
                    ''', conn)

            df.to_csv(filename, index=False, encoding='utf-8-sig')
            self.logger.info(f"Exported {len(df)} records to {filename}")
            return True

        except Exception as e:
            self.logger.error(f"Error exporting to CSV: {e}")
            return False

    def export_to_json(self, filename=JSON_OUTPUT):
        """Export dữ liệu ra file JSON"""
        try:
            if self.use_mysql:
                connection = mysql.connector.connect(**self.mysql_config)
                cursor = connection.cursor()
                cursor.execute('''
                    SELECT * FROM cars
                    ORDER BY brand, model, year DESC
                ''')

                columns = [description[0] for description in cursor.description]
                cars = []

                for row in cursor.fetchall():
                    car_dict = dict(zip(columns, row))
                    # Parse images JSON
                    if car_dict.get('images'):
                        try:
                            if isinstance(car_dict['images'], str):
                                car_dict['images'] = json.loads(car_dict['images'])
                        except:
                            car_dict['images'] = []
                    cars.append(car_dict)

                connection.close()
            else:
                with sqlite3.connect(self.db_name) as conn:
                    cursor = conn.cursor()
                    cursor.execute('''
                        SELECT * FROM cars
                        ORDER BY brand, model, year DESC
                    ''')

                    columns = [description[0] for description in cursor.description]
                    cars = []

                    for row in cursor.fetchall():
                        car_dict = dict(zip(columns, row))
                        # Parse images JSON
                        if car_dict.get('images'):
                            try:
                                car_dict['images'] = json.loads(car_dict['images'])
                            except:
                                car_dict['images'] = []
                        cars.append(car_dict)

            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(cars, f, ensure_ascii=False, indent=2, default=str)

            self.logger.info(f"Exported {len(cars)} records to {filename}")
            return True

        except Exception as e:
            self.logger.error(f"Error exporting to JSON: {e}")
            return False
