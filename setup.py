#!/usr/bin/env python3
"""
Setup script cho <PERSON>h Car Scraper
"""

import subprocess
import sys
import os
from pathlib import Path

def install_requirements():
    """Cài đặt các dependencies"""
    print("📦 Đang cài đặt dependencies...")
    
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Dependencies đã được cài đặt thành công!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Lỗi khi cài đặt dependencies: {e}")
        return False

def check_env_file():
    """Kiểm tra file .env"""
    env_file = Path(".env")
    
    if not env_file.exists():
        print("⚠️  File .env không tồn tại!")
        print("Tạo file .env với nội dung sau:")
        print("""
# Database Configuration
DB_HOST=127.0.0.1
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=your_password_here
DB_DATABASE=xehoi_pro
        """)
        return False
    else:
        print("✅ File .env đã tồn tại")
        return True

def setup_mysql():
    """Setup MySQL database"""
    print("🗄️  Đang setup MySQL database...")
    
    try:
        subprocess.check_call([sys.executable, "create_mysql_tables.py"])
        print("✅ MySQL database đã được setup thành công!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Lỗi khi setup MySQL: {e}")
        print("Vui lòng kiểm tra:")
        print("1. MySQL server đang chạy")
        print("2. Thông tin kết nối trong file .env đúng")
        print("3. User có quyền tạo database")
        return False

def run_test():
    """Chạy test để kiểm tra"""
    print("🧪 Đang chạy test...")
    
    try:
        subprocess.check_call([sys.executable, "test_scraper.py"])
        print("✅ Tất cả tests đều PASS!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Một số tests FAIL: {e}")
        return False

def main():
    """Main setup function"""
    print("🚗 BONBANH CAR SCRAPER - SETUP")
    print("=" * 50)
    
    steps = [
        ("Cài đặt dependencies", install_requirements),
        ("Kiểm tra file .env", check_env_file),
        ("Setup MySQL database", setup_mysql),
        ("Chạy tests", run_test)
    ]
    
    for step_name, step_func in steps:
        print(f"\n{step_name}...")
        if not step_func():
            print(f"❌ Setup thất bại tại bước: {step_name}")
            return False
    
    print("\n" + "=" * 50)
    print("🎉 SETUP HOÀN THÀNH!")
    print("\nBạn có thể chạy scraper bằng các lệnh sau:")
    print("  python main.py --mode test     # Test scraping")
    print("  python main.py --mode full     # Full scraping")
    print("  python main.py --mode analyze  # Chỉ phân tích dữ liệu")
    
    return True

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
