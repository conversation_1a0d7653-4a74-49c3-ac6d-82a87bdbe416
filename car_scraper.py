"""
Module để scrape chi tiết từng listing xe từ bonbanh.com
"""

from scraper_base import <PERSON><PERSON>hScrap<PERSON>
from config import *
import re
from urllib.parse import urljoin


class CarScraper(BonbanhScraper):
    """Class để scrape thông tin chi tiết từng xe"""

    def __init__(self):
        super().__init__()

    def get_car_listings_from_page(self, page_url):
        """
        Lấy danh sách các listing xe từ một trang
        """
        response = self.get_page(page_url)
        soup = self.parse_html(response)

        if not soup:
            return []

        listings = []

        # Tìm các link đến chi tiết xe (pattern: xe-brand-model-year-id)
        car_links = soup.find_all('a', href=re.compile(r'xe-.*-\d{4}-\d+'))

        # Nếu không tìm thấy, thử pattern khác
        if not car_links:
            # Tìm tất cả links có chứa "xe-" và kết thúc bằng số
            car_links = soup.find_all('a', href=re.compile(r'xe-.+-\d+$'))

        for link in car_links:
            href = link.get('href', '')
            if href:
                full_url = self.build_url(href)
                listings.append(full_url)

        self.logger.info(f"Tìm thấy {len(listings)} listings trong trang")
        return listings

    def get_all_listings_for_brand(self, brand_url, max_pages=None):
        """
        Lấy tất cả listings cho một hãng xe với pagination
        """
        if max_pages is None:
            max_pages = MAX_PAGES_PER_BRAND

        all_listings = []
        page = 1

        while page <= max_pages:
            # Tạo URL cho trang cụ thể
            if page == 1:
                page_url = brand_url
            else:
                page_url = f"{brand_url}?page={page}"

            self.logger.info(f"Đang scrape trang {page} của {brand_url}")

            listings = self.get_car_listings_from_page(page_url)

            if not listings:
                self.logger.info(f"Không có listings ở trang {page}, dừng pagination")
                break

            all_listings.extend(listings)
            page += 1

        self.logger.info(f"Tổng cộng tìm thấy {len(all_listings)} listings")
        return all_listings

    def scrape_car_detail(self, car_url):
        """
        Scrape thông tin chi tiết của một xe
        """
        response = self.get_page(car_url)
        soup = self.parse_html(response)

        if not soup:
            return None

        car_data = {
            'url': car_url,
            'listing_id': self.extract_listing_id(car_url),
            'title': '',
            'brand': '',
            'model': '',
            'year': None,
            'price': None,
            'price_text': '',
            'condition': '',
            'km': None,
            'fuel_type': '',
            'transmission': '',
            'origin': '',
            'color_exterior': '',
            'color_interior': '',
            'seats': None,
            'doors': None,
            'drivetrain': '',
            'description': '',
            'seller_name': '',
            'seller_phone': '',
            'seller_address': '',
            'images': []
        }

        try:
            # Tiêu đề xe
            title_elem = soup.find('h1') or soup.find('title')
            if title_elem:
                car_data['title'] = self.clean_text(title_elem.get_text())

            # Extract brand, model, year từ title hoặc URL
            self.extract_brand_model_year(car_data)

            # Giá xe
            price_elem = soup.find(text=re.compile(r'(Tỷ|Triệu)'))
            if price_elem:
                price_container = price_elem.parent
                if price_container:
                    price_text = self.clean_text(price_container.get_text())
                    car_data['price_text'] = price_text
                    car_data['price'] = self.extract_price(price_text)

            # Thông số kỹ thuật
            self.extract_technical_specs(soup, car_data)

            # Thông tin người bán
            self.extract_seller_info(soup, car_data)

            # Mô tả
            desc_elem = soup.find('div', class_=re.compile(r'description|content|detail'))
            if desc_elem:
                car_data['description'] = self.clean_text(desc_elem.get_text())

            # Hình ảnh
            car_data['images'] = self.extract_images(soup)

        except Exception as e:
            self.logger.error(f"Lỗi khi scrape {car_url}: {e}")

        return car_data

    def extract_listing_id(self, url):
        """Extract ID từ URL"""
        match = re.search(r'-(\d+)$', url)
        return match.group(1) if match else None

    def extract_brand_model_year(self, car_data):
        """Extract brand, model, year từ title hoặc URL"""
        title = car_data['title']
        url = car_data['url']

        # Từ URL: /xe-brand-model-year-id
        url_match = re.search(r'/xe-([a-zA-Z_]+)-(.+)-(\d{4})-\d+$', url)
        if url_match:
            car_data['brand'] = url_match.group(1).replace('_', ' ').title()
            model_part = url_match.group(2).replace('-', ' ').title()
            car_data['model'] = model_part
            car_data['year'] = int(url_match.group(3))

        # Từ title nếu chưa có
        if not car_data['year']:
            year = self.extract_year(title)
            if year:
                car_data['year'] = year

    def extract_technical_specs(self, soup, car_data):
        """Extract thông số kỹ thuật từ trang chi tiết"""
        # Tìm bảng thông số kỹ thuật
        spec_section = soup.find('div', text=re.compile('Thông số kỹ thuật'))
        if spec_section:
            spec_container = spec_section.find_next('div') or spec_section.parent

            # Extract các thông số
            specs = spec_container.find_all('tr') if spec_container else []

            for spec in specs:
                cells = spec.find_all(['td', 'th'])
                if len(cells) >= 2:
                    key = self.clean_text(cells[0].get_text()).lower()
                    value = self.clean_text(cells[1].get_text())

                    if 'tình trạng' in key:
                        car_data['condition'] = value
                    elif 'km' in key or 'đã đi' in key:
                        car_data['km'] = self.extract_km(value)
                    elif 'động cơ' in key or 'nhiên liệu' in key:
                        car_data['fuel_type'] = value
                    elif 'hộp số' in key:
                        car_data['transmission'] = value
                    elif 'xuất xứ' in key:
                        car_data['origin'] = value
                    elif 'màu ngoại thất' in key:
                        car_data['color_exterior'] = value
                    elif 'màu nội thất' in key:
                        car_data['color_interior'] = value
                    elif 'số chỗ' in key:
                        try:
                            car_data['seats'] = int(re.search(r'\d+', value).group())
                        except:
                            pass
                    elif 'số cửa' in key:
                        try:
                            car_data['doors'] = int(re.search(r'\d+', value).group())
                        except:
                            pass
                    elif 'dẫn động' in key:
                        car_data['drivetrain'] = value

    def extract_seller_info(self, soup, car_data):
        """Extract thông tin người bán"""
        # Tìm section thông tin liên hệ
        contact_section = soup.find('div', text=re.compile('Liên hệ người bán'))
        if contact_section:
            contact_container = contact_section.find_next('div') or contact_section.parent

            # Tên người bán
            name_elem = contact_container.find('strong') if contact_container else None
            if name_elem:
                car_data['seller_name'] = self.clean_text(name_elem.get_text())

            # Số điện thoại
            phone_elem = contact_container.find('a', href=re.compile(r'tel:')) if contact_container else None
            if phone_elem:
                car_data['seller_phone'] = self.clean_text(phone_elem.get_text())

            # Địa chỉ
            address_text = contact_container.get_text() if contact_container else ""
            lines = address_text.split('\n')
            for line in lines:
                line = self.clean_text(line)
                if line and 'Địa chỉ:' in line:
                    car_data['seller_address'] = line.replace('Địa chỉ:', '').strip()
                    break

    def extract_images(self, soup):
        """Extract danh sách hình ảnh"""
        images = []

        # Tìm tất cả img tags trong gallery
        img_elements = soup.find_all('img', src=re.compile(r'uploads/users/.*\.(jpg|jpeg|png|gif)'))

        for img in img_elements:
            src = img.get('src', '')
            if src:
                full_url = self.build_url(src)
                if full_url not in images:
                    images.append(full_url)

        return images
