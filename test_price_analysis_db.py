#!/usr/bin/env python3
"""
Test script để kiểm tra việc lưu price analysis vào MySQL
"""

import sys
import logging
from price_analyzer import PriceAnalyzer

def test_price_analysis_db():
    """Test lưu price analysis vào database"""
    
    print("📊 TESTING PRICE ANALYSIS DATABASE SAVE")
    print("=" * 60)
    
    # Setup logging
    logging.basicConfig(level=logging.INFO)
    
    # Khởi tạo analyzer với MySQL
    analyzer = PriceAnalyzer(use_mysql=True)
    
    print("1. Tính toán và lưu price analysis...")
    
    # Test save price analysis
    if analyzer.save_price_analysis_to_db():
        print("   ✅ Lưu price analysis thành công")
    else:
        print("   ❌ Lưu price analysis thất bại")
        return False
    
    print("\n2. Export price analysis (bao gồm save to DB)...")
    
    # Test export với save to DB
    if analyzer.export_price_analysis(save_to_db=True):
        print("   ✅ Export và lưu DB thành công")
    else:
        print("   ❌ Export thất bại")
        return False
    
    print("\n3. Kiểm tra dữ liệu trong database...")
    
    try:
        import mysql.connector
        import os
        from dotenv import load_dotenv
        
        load_dotenv()
        
        mysql_config = {
            'host': os.getenv('DB_HOST', '127.0.0.1'),
            'port': int(os.getenv('DB_PORT', 3306)),
            'database': os.getenv('DB_DATABASE', 'xehoi_pro'),
            'user': os.getenv('DB_USERNAME', 'root'),
            'password': os.getenv('DB_PASSWORD', 'pwdpwd')
        }
        
        connection = mysql.connector.connect(**mysql_config)
        cursor = connection.cursor()
        
        # Check total records
        cursor.execute("SELECT COUNT(*) FROM price_analysis")
        total_count = cursor.fetchone()[0]
        print(f"   Tổng số records: {total_count}")
        
        # Check by analysis type
        cursor.execute("""
            SELECT analysis_type, COUNT(*) as count 
            FROM price_analysis 
            GROUP BY analysis_type
        """)
        type_counts = cursor.fetchall()
        
        for analysis_type, count in type_counts:
            print(f"   - {analysis_type}: {count} records")
        
        print("\n   📋 BRAND ANALYSIS:")
        cursor.execute("""
            SELECT brand, count, avg_price, min_price, max_price
            FROM price_analysis 
            WHERE analysis_type = 'brand'
            ORDER BY avg_price DESC
        """)
        brand_analysis = cursor.fetchall()
        
        for brand, count, avg_price, min_price, max_price in brand_analysis:
            print(f"     - {brand}: {count} xe, TB: {avg_price:,} VND ({min_price:,} - {max_price:,})")
        
        print("\n   🚗 MODEL ANALYSIS (Top 10):")
        cursor.execute("""
            SELECT brand, model, count, avg_price
            FROM price_analysis 
            WHERE analysis_type = 'model'
            ORDER BY avg_price DESC
            LIMIT 10
        """)
        model_analysis = cursor.fetchall()
        
        for brand, model, count, avg_price in model_analysis:
            print(f"     - {brand} {model}: {count} xe, TB: {avg_price:,} VND")
        
        print("\n   📅 YEAR ANALYSIS (Top 10):")
        cursor.execute("""
            SELECT brand, model, year, count, avg_price
            FROM price_analysis 
            WHERE analysis_type = 'year'
            ORDER BY year DESC, avg_price DESC
            LIMIT 10
        """)
        year_analysis = cursor.fetchall()
        
        for brand, model, year, count, avg_price in year_analysis:
            print(f"     - {brand} {model} {year}: {count} xe, TB: {avg_price:,} VND")
        
        connection.close()
        
        print("\n" + "=" * 60)
        print("SUMMARY:")
        print(f"  📊 Total analysis records: {total_count}")
        print(f"  📁 Brand analyses: {dict(type_counts).get('brand', 0)}")
        print(f"  🚗 Model analyses: {dict(type_counts).get('model', 0)}")
        print(f"  📅 Year analyses: {dict(type_counts).get('year', 0)}")
        
        if total_count > 0:
            print("🎉 PRICE ANALYSIS DATABASE SAVE SUCCESSFUL!")
            return True
        else:
            print("❌ NO DATA SAVED TO DATABASE!")
            return False
        
    except Exception as e:
        print(f"   ❌ Lỗi khi kiểm tra database: {e}")
        return False

def test_query_analysis():
    """Test query dữ liệu phân tích"""
    
    print("\n" + "=" * 60)
    print("📈 TESTING ANALYSIS QUERIES")
    print("=" * 60)
    
    try:
        import mysql.connector
        import os
        from dotenv import load_dotenv
        
        load_dotenv()
        
        mysql_config = {
            'host': os.getenv('DB_HOST', '127.0.0.1'),
            'port': int(os.getenv('DB_PORT', 3306)),
            'database': os.getenv('DB_DATABASE', 'xehoi_pro'),
            'user': os.getenv('DB_USERNAME', 'root'),
            'password': os.getenv('DB_PASSWORD', 'pwdpwd')
        }
        
        connection = mysql.connector.connect(**mysql_config)
        cursor = connection.cursor()
        
        # Query 1: Brands với giá cao nhất
        print("1. Top 5 brands có giá trung bình cao nhất:")
        cursor.execute("""
            SELECT brand, avg_price, count
            FROM price_analysis 
            WHERE analysis_type = 'brand'
            ORDER BY avg_price DESC
            LIMIT 5
        """)
        
        for brand, avg_price, count in cursor.fetchall():
            print(f"   {brand}: {avg_price:,} VND ({count} xe)")
        
        # Query 2: Models phổ biến nhất
        print("\n2. Top 5 models có nhiều xe nhất:")
        cursor.execute("""
            SELECT brand, model, count, avg_price
            FROM price_analysis 
            WHERE analysis_type = 'model'
            ORDER BY count DESC
            LIMIT 5
        """)
        
        for brand, model, count, avg_price in cursor.fetchall():
            print(f"   {brand} {model}: {count} xe, TB: {avg_price:,} VND")
        
        # Query 3: Năm sản xuất phổ biến
        print("\n3. Top 5 năm sản xuất phổ biến:")
        cursor.execute("""
            SELECT year, SUM(count) as total_count, AVG(avg_price) as overall_avg
            FROM price_analysis 
            WHERE analysis_type = 'year' AND year IS NOT NULL
            GROUP BY year
            ORDER BY total_count DESC
            LIMIT 5
        """)
        
        for year, total_count, overall_avg in cursor.fetchall():
            print(f"   {year}: {total_count} xe, TB: {overall_avg:,} VND")
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ Lỗi khi query analysis: {e}")
        return False

if __name__ == '__main__':
    print("🧪 PRICE ANALYSIS DATABASE TESTING")
    print("=" * 60)
    
    success1 = test_price_analysis_db()
    success2 = test_query_analysis()
    
    overall_success = success1 and success2
    
    print("\n" + "=" * 60)
    if overall_success:
        print("🎉 ALL TESTS PASSED!")
    else:
        print("❌ SOME TESTS FAILED!")
    
    sys.exit(0 if overall_success else 1)
