"""
<PERSON><PERSON><PERSON> hình cho Bonbanh Car Scraper
"""

# URL c<PERSON> bản
BASE_URL = "https://bonbanh.com"
CARS_URL = f"{BASE_URL}/oto"

# Headers để tránh bị block
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
    'Accept-Language': 'vi-VN,vi;q=0.9,en;q=0.8',
    'Accept-Encoding': 'gzip, deflate, br',
    'Connection': 'keep-alive',
    'Upgrade-Insecure-Requests': '1',
}

# Cấu hình delay để tránh quá tải server
MIN_DELAY = 1  # giây
MAX_DELAY = 3  # giây

# Cấu hình retry
MAX_RETRIES = 3
RETRY_DELAY = 5  # giây

# Cấu hình database
DATABASE_NAME = "bonbanh_cars.db"

# MySQL Configuration
MYSQL_CONFIG = {
    'host': '127.0.0.1',
    'port': 3306,
    'database': 'xehoi_pro',
    'username': 'root',
    'password': 'pwdpwd'
}

# Cấu hình logging
LOG_LEVEL = "INFO"
LOG_FILE = "scraper.log"

# Cấu hình pagination
MAX_PAGES_PER_BRAND = 50  # Giới hạn số trang để tránh scrape quá lâu

# Danh sách các hãng xe phổ biến để ưu tiên scrape
PRIORITY_BRANDS = [
    'toyota', 'honda', 'mazda', 'hyundai', 'kia', 'ford', 'chevrolet',
    'nissan', 'mitsubishi', 'suzuki', 'mercedes_benz', 'bmw', 'audi',
    'lexus', 'vinfast'
]

# Regex patterns để extract dữ liệu
PRICE_PATTERN = r'(\d+(?:\.\d+)*)\s*(?:Tỷ|Triệu)'
YEAR_PATTERN = r'(19|20)\d{2}'
KM_PATTERN = r'(\d+(?:,\d+)*)\s*km'

# File output
CSV_OUTPUT = "bonbanh_cars_data.csv"
JSON_OUTPUT = "bonbanh_cars_data.json"
SUMMARY_OUTPUT = "price_summary.json"
