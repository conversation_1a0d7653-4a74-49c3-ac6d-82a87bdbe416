#!/usr/bin/env python3
"""
Script tạo bảng MySQL cho Bonbanh Car Scraper
"""

import mysql.connector
from mysql.connector import Error
import os
from dotenv import load_dotenv
import logging

# Load environment variables
load_dotenv()

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_database_and_tables():
    """Tạo database và các bảng cần thiết"""
    
    # MySQL configuration
    mysql_config = {
        'host': os.getenv('DB_HOST', '127.0.0.1'),
        'port': int(os.getenv('DB_PORT', 3306)),
        'user': os.getenv('DB_USERNAME', 'root'),
        'password': os.getenv('DB_PASSWORD', 'pwdpwd'),
        'charset': 'utf8mb4',
        'collation': 'utf8mb4_unicode_ci'
    }
    
    database_name = os.getenv('DB_DATABASE', 'xehoi_pro')
    
    try:
        # Kết nối MySQL server (không chỉ định database)
        connection = mysql.connector.connect(**mysql_config)
        cursor = connection.cursor()
        
        # Tạo database nếu chưa tồn tại
        cursor.execute(f"CREATE DATABASE IF NOT EXISTS {database_name} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
        logger.info(f"Database '{database_name}' created or already exists")
        
        # Chọn database
        cursor.execute(f"USE {database_name}")
        
        # Tạo bảng cars
        create_cars_table = '''
        CREATE TABLE IF NOT EXISTS cars (
            id INT AUTO_INCREMENT PRIMARY KEY,
            listing_id VARCHAR(50) UNIQUE,
            url TEXT,
            title TEXT,
            brand VARCHAR(100),
            model VARCHAR(100),
            year INT,
            price BIGINT,
            price_text VARCHAR(100),
            `condition` VARCHAR(50),
            km INT,
            fuel_type VARCHAR(50),
            transmission VARCHAR(50),
            origin VARCHAR(50),
            color_exterior VARCHAR(50),
            color_interior VARCHAR(50),
            seats INT,
            doors INT,
            drivetrain VARCHAR(50),
            description TEXT,
            seller_name VARCHAR(200),
            seller_phone VARCHAR(50),
            seller_address TEXT,
            images JSON,
            scraped_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        '''
        
        cursor.execute(create_cars_table)
        logger.info("Table 'cars' created successfully")
        
        # Tạo bảng brands
        create_brands_table = '''
        CREATE TABLE IF NOT EXISTS brands (
            id INT AUTO_INCREMENT PRIMARY KEY,
            slug VARCHAR(100) UNIQUE,
            name VARCHAR(200),
            url TEXT,
            scraped_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        '''
        
        cursor.execute(create_brands_table)
        logger.info("Table 'brands' created successfully")
        
        # Tạo bảng models
        create_models_table = '''
        CREATE TABLE IF NOT EXISTS models (
            id INT AUTO_INCREMENT PRIMARY KEY,
            brand_slug VARCHAR(100),
            slug VARCHAR(100),
            name VARCHAR(200),
            url TEXT,
            full_slug VARCHAR(200),
            scraped_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (brand_slug) REFERENCES brands (slug) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        '''
        
        cursor.execute(create_models_table)
        logger.info("Table 'models' created successfully")
        
        # Tạo indexes
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_cars_brand_model_year ON cars (brand, model, year)",
            "CREATE INDEX IF NOT EXISTS idx_cars_price ON cars (price)",
            "CREATE INDEX IF NOT EXISTS idx_cars_year ON cars (year)",
            "CREATE INDEX IF NOT EXISTS idx_cars_listing_id ON cars (listing_id)",
            "CREATE INDEX IF NOT EXISTS idx_brands_slug ON brands (slug)",
            "CREATE INDEX IF NOT EXISTS idx_models_brand_slug ON models (brand_slug)",
            "CREATE INDEX IF NOT EXISTS idx_cars_scraped_at ON cars (scraped_at)"
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
            
        logger.info("All indexes created successfully")
        
        # Tạo bảng price_analysis để lưu kết quả phân tích
        create_price_analysis_table = '''
        CREATE TABLE IF NOT EXISTS price_analysis (
            id INT AUTO_INCREMENT PRIMARY KEY,
            analysis_type ENUM('brand', 'model', 'year') NOT NULL,
            brand VARCHAR(100),
            model VARCHAR(100),
            year INT,
            count INT,
            avg_price BIGINT,
            median_price BIGINT,
            min_price BIGINT,
            max_price BIGINT,
            std_price BIGINT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        '''
        
        cursor.execute(create_price_analysis_table)
        logger.info("Table 'price_analysis' created successfully")
        
        # Tạo indexes cho price_analysis
        price_analysis_indexes = [
            "CREATE INDEX IF NOT EXISTS idx_price_analysis_type ON price_analysis (analysis_type)",
            "CREATE INDEX IF NOT EXISTS idx_price_analysis_brand ON price_analysis (brand)",
            "CREATE INDEX IF NOT EXISTS idx_price_analysis_model ON price_analysis (brand, model)",
            "CREATE INDEX IF NOT EXISTS idx_price_analysis_year ON price_analysis (brand, model, year)"
        ]
        
        for index_sql in price_analysis_indexes:
            cursor.execute(index_sql)
            
        connection.commit()
        logger.info("All tables and indexes created successfully!")
        
        # Hiển thị thông tin tables
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()
        logger.info(f"Tables in database '{database_name}':")
        for table in tables:
            logger.info(f"  - {table[0]}")
            
    except Error as e:
        logger.error(f"Error creating database and tables: {e}")
        return False
        
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()
            logger.info("MySQL connection closed")
            
    return True

def test_connection():
    """Test kết nối MySQL"""
    mysql_config = {
        'host': os.getenv('DB_HOST', '127.0.0.1'),
        'port': int(os.getenv('DB_PORT', 3306)),
        'database': os.getenv('DB_DATABASE', 'xehoi_pro'),
        'user': os.getenv('DB_USERNAME', 'root'),
        'password': os.getenv('DB_PASSWORD', 'pwdpwd')
    }
    
    try:
        connection = mysql.connector.connect(**mysql_config)
        if connection.is_connected():
            db_info = connection.get_server_info()
            logger.info(f"Successfully connected to MySQL Server version {db_info}")
            
            cursor = connection.cursor()
            cursor.execute("SELECT DATABASE();")
            database_name = cursor.fetchone()
            logger.info(f"Connected to database: {database_name[0]}")
            
            return True
            
    except Error as e:
        logger.error(f"Error connecting to MySQL: {e}")
        return False
        
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()

if __name__ == '__main__':
    print("=== BONBANH CAR SCRAPER - MYSQL SETUP ===")
    print()
    
    # Test connection first
    print("1. Testing MySQL connection...")
    if not test_connection():
        print("❌ Cannot connect to MySQL. Please check your configuration in .env file")
        exit(1)
    else:
        print("✅ MySQL connection successful")
    
    print()
    print("2. Creating database and tables...")
    if create_database_and_tables():
        print("✅ Database and tables created successfully!")
        print()
        print("🎉 Setup completed! You can now run the scraper with MySQL support.")
    else:
        print("❌ Failed to create database and tables")
        exit(1)
