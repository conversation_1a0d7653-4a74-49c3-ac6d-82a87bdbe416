"""
Module cơ bản cho web scraping bonbanh.com
"""

import requests
import time
import random
import logging
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
import re
from config import *

class BonbanhScraper:
    """Class cơ bản để scrape dữ liệu từ bonbanh.com"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update(HEADERS)
        self.setup_logging()
        
    def setup_logging(self):
        """Thiết lập logging"""
        logging.basicConfig(
            level=getattr(logging, LOG_LEVEL),
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(LOG_FILE, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def get_page(self, url, retries=0):
        """
        Lấy nội dung trang web với retry logic
        """
        try:
            self.logger.info(f"<PERSON><PERSON> truy cập: {url}")
            
            # Random delay để tránh bị block
            delay = random.uniform(MIN_DELAY, MAX_DELAY)
            time.sleep(delay)
            
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            
            # Kiểm tra encoding
            if response.encoding.lower() != 'utf-8':
                response.encoding = 'utf-8'
                
            return response
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Lỗi khi truy cập {url}: {e}")
            
            if retries < MAX_RETRIES:
                self.logger.info(f"Thử lại lần {retries + 1}/{MAX_RETRIES}")
                time.sleep(RETRY_DELAY)
                return self.get_page(url, retries + 1)
            else:
                self.logger.error(f"Đã thử {MAX_RETRIES} lần, bỏ qua URL: {url}")
                return None
                
    def parse_html(self, response):
        """Parse HTML response thành BeautifulSoup object"""
        if response is None:
            return None
            
        try:
            soup = BeautifulSoup(response.text, 'lxml')
            return soup
        except Exception as e:
            self.logger.error(f"Lỗi khi parse HTML: {e}")
            return None
            
    def extract_price(self, price_text):
        """
        Trích xuất giá từ text và chuyển về VND
        """
        if not price_text:
            return None
            
        # Tìm pattern giá
        match = re.search(PRICE_PATTERN, price_text.replace(',', '.'))
        if not match:
            return None
            
        try:
            price_value = float(match.group(1))
            
            # Chuyển đổi đơn vị
            if 'Tỷ' in price_text:
                return int(price_value * 1_000_000_000)
            elif 'Triệu' in price_text:
                return int(price_value * 1_000_000)
            else:
                return int(price_value)
                
        except (ValueError, AttributeError):
            self.logger.warning(f"Không thể parse giá: {price_text}")
            return None
            
    def extract_year(self, text):
        """Trích xuất năm sản xuất từ text"""
        if not text:
            return None
            
        match = re.search(YEAR_PATTERN, text)
        if match:
            year = int(match.group(0))
            # Kiểm tra năm hợp lệ (1990-2025)
            if 1990 <= year <= 2025:
                return year
        return None
        
    def extract_km(self, text):
        """Trích xuất số km đã đi từ text"""
        if not text:
            return None
            
        match = re.search(KM_PATTERN, text.replace(',', ''))
        if match:
            try:
                return int(match.group(1).replace(',', ''))
            except ValueError:
                pass
        return None
        
    def clean_text(self, text):
        """Làm sạch text"""
        if not text:
            return ""
        return re.sub(r'\s+', ' ', text.strip())
        
    def build_url(self, path):
        """Tạo URL đầy đủ từ path"""
        return urljoin(BASE_URL, path)
        
    def is_valid_url(self, url):
        """Kiểm tra URL có hợp lệ không"""
        try:
            result = urlparse(url)
            return all([result.scheme, result.netloc])
        except:
            return False
