# Workflow: Bonbanh Car Scraper

## Tổng quan

Công cụ web scraping để thu thập dữ liệu xe hơi cũ từ bonbanh.com với khả năng lưu trữ vào MySQL database và phân tích giá.

## Cập nhật mới nhất

- ✅ Đã tích hợp MySQL database thay thế SQLite
- ✅ Hỗ trợ cả MySQL và SQLite (fallback)
- ✅ Tạo script setup tự động
- ✅ Cải thiện error handling và logging
- ✅ Thêm demo script cho full scraping
- ✅ **Hoàn thành MySQL integration cho brands và models**
- ✅ **Tested và verified với dữ liệu thực tế**

## Cấu trúc dự án

### Files chính

- `main.py` - Script chính điều khiển toàn bộ quá trình
- `config.py` - <PERSON><PERSON><PERSON> hì<PERSON> hệ thống
- `scraper_base.py` - Base class cho web scraping
- `brand_scraper.py` - Scraper cho brands và models
- `car_scraper.py` - Scraper cho chi tiết xe
- `database.py` - Quản lý database (MySQL/SQLite)
- `price_analyzer.py` - Phân tích giá và thống kê
- `create_mysql_tables.py` - Script tạo bảng MySQL
- `setup.py` - Script setup tự động
- `test_scraper.py` - Script test các components
- `demo_full_scraping.py` - Demo script cho full scraping

### Files cấu hình

- `.env` - Cấu hình database và môi trường
- `requirements.txt` - Dependencies Python
- `README.md` - Hướng dẫn sử dụng
- `MYSQL_SETUP.md` - Hướng dẫn setup MySQL

## Cấu hình Database

### MySQL Configuration (.env)

```env
DB_HOST=127.0.0.1
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=pwdpwd
DB_DATABASE=xehoi_pro
```

### Database Schema

#### Bảng `cars`

- `id` - Primary key
- `listing_id` - ID unique của listing
- `url` - URL của listing
- `title` - Tiêu đề xe
- `brand` - Hãng xe
- `model` - Model xe
- `year` - Năm sản xuất
- `price` - Giá (VND)
- `price_text` - Text giá gốc
- `condition` - Tình trạng xe
- `km` - Số km đã đi
- `fuel_type` - Loại nhiên liệu
- `transmission` - Hộp số
- `origin` - Xuất xứ
- `color_exterior` - Màu ngoại thất
- `color_interior` - Màu nội thất
- `seats` - Số chỗ ngồi
- `doors` - Số cửa
- `drivetrain` - Hệ dẫn động
- `description` - Mô tả
- `seller_name` - Tên người bán
- `seller_phone` - SĐT người bán
- `seller_address` - Địa chỉ người bán
- `images` - JSON array hình ảnh
- `scraped_at` - Thời gian scrape
- `updated_at` - Thời gian cập nhật

#### Bảng `brands`

- `id` - Primary key
- `slug` - Slug của brand
- `name` - Tên brand
- `url` - URL của brand
- `scraped_at` - Thời gian scrape

#### Bảng `models`

- `id` - Primary key
- `brand_slug` - Foreign key đến brands
- `slug` - Slug của model
- `name` - Tên model
- `url` - URL của model
- `full_slug` - Full slug (brand-model)
- `scraped_at` - Thời gian scrape

#### Bảng `price_analysis`

- `id` - Primary key
- `analysis_type` - Loại phân tích (brand/model/year)
- `brand` - Hãng xe
- `model` - Model xe
- `year` - Năm sản xuất
- `count` - Số lượng xe
- `avg_price` - Giá trung bình
- `median_price` - Giá trung vị
- `min_price` - Giá thấp nhất
- `max_price` - Giá cao nhất
- `std_price` - Độ lệch chuẩn
- `created_at` - Thời gian tạo

## Quy trình Scraping

### 1. Setup MySQL

```bash
# Cài đặt dependencies
pip install -r requirements.txt

# Tạo file .env với thông tin database MySQL
# DB_HOST=127.0.0.1
# DB_PORT=3306
# DB_USERNAME=root
# DB_PASSWORD=your_password
# DB_DATABASE=xehoi_pro

# Chạy setup tự động (bao gồm tạo database)
python setup.py
```

### 2. Tạo Database (manual)

```bash
# Tạo database và tables MySQL
python create_mysql_tables.py
```

### 3. Chạy Scraper

#### Test Mode (ít dữ liệu)

```bash
python main.py --mode test
```

#### Full Mode (tất cả dữ liệu)

```bash
python main.py --mode full
```

#### Scrape brands cụ thể

```bash
python main.py --mode full --brands toyota honda mazda
```

#### Giới hạn số xe mỗi brand

```bash
python main.py --mode full --max-cars 50
```

#### Chỉ phân tích dữ liệu có sẵn

```bash
python main.py --mode analyze
```

#### Demo Full Scraping (Interactive)

```bash
python demo_full_scraping.py
```

### 4. Test Components

```bash
python test_scraper.py
```

## Tính năng chính

### Web Scraping

- Thu thập danh sách brands và models
- Scrape chi tiết từng listing xe
- Xử lý pagination tự động
- Rate limiting để tránh quá tải server
- Retry logic cho network errors
- Xử lý encoding tiếng Việt

### Data Processing

- Extract thông tin từ HTML
- Parse giá tiền (Tỷ/Triệu → VND)
- Extract năm sản xuất, km, thông số kỹ thuật
- Làm sạch và chuẩn hóa dữ liệu

### Database Management

- Hỗ trợ cả MySQL và SQLite
- UPSERT operations (INSERT ON DUPLICATE KEY UPDATE)
- JSON storage cho images
- Indexes để tối ưu query
- Automatic timestamps

### Price Analysis

- Tính giá trung bình theo brand/model/year
- Phân tích xu hướng giá
- Tìm best deals (xe giá tốt)
- Export báo cáo JSON/CSV

## Output Files

### Data Export

- `bonbanh_cars_data.csv` - Dữ liệu xe dạng CSV
- `bonbanh_cars_data.json` - Dữ liệu xe dạng JSON
- `price_summary.json` - Phân tích giá chi tiết

### Logs

- `scraper.log` - Log file chi tiết

## Cấu hình quan trọng

### Rate Limiting

```python
MIN_DELAY = 1  # giây
MAX_DELAY = 3  # giây
MAX_RETRIES = 3
RETRY_DELAY = 5  # giây
```

### Pagination

```python
MAX_PAGES_PER_BRAND = 50  # Giới hạn số trang
```

### Priority Brands

```python
PRIORITY_BRANDS = [
    'toyota', 'honda', 'mazda', 'hyundai', 'kia',
    'ford', 'chevrolet', 'nissan', 'mitsubishi',
    'suzuki', 'mercedes_benz', 'bmw', 'audi',
    'lexus', 'vinfast'
]
```

## Troubleshooting

### Lỗi thường gặp

1. **MySQL Connection Error**

   - Kiểm tra MySQL server đang chạy
   - Verify thông tin trong .env file
   - Kiểm tra quyền user

2. **No listings found**

   - Website có thể đã thay đổi cấu trúc
   - Cần update regex patterns trong car_scraper.py

3. **Encoding issues**
   - Đảm bảo database charset là utf8mb4
   - Python encoding được set đúng

### Debug

```python
# Bật debug logging
LOG_LEVEL = "DEBUG"

# Chạy debug script
python debug_scraper.py
```

## Performance Tips

1. **Database Optimization**

   - Sử dụng batch inserts
   - Tạo indexes phù hợp
   - Regular ANALYZE TABLE

2. **Scraping Optimization**

   - Tăng delay nếu bị block
   - Sử dụng session để reuse connections
   - Monitor memory usage

3. **Data Analysis**
   - Cache kết quả phân tích
   - Sử dụng pandas cho large datasets
   - Parallel processing cho multiple brands

## Bảo mật và Tuân thủ

1. **Respect robots.txt**
2. **Rate limiting để tránh quá tải server**
3. **Không scrape quá nhiều concurrent requests**
4. **Tuân thủ terms of service của bonbanh.com**

## Mở rộng tương lai

1. **Multi-threading scraping**
2. **Real-time price alerts**
3. **Machine learning price prediction**
4. **Web dashboard cho data visualization**
5. **API endpoints cho external access**
6. **Scheduled scraping với cron jobs**
