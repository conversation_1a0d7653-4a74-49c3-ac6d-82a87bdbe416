#!/usr/bin/env python3
"""
Bonbanh Car Scraper - <PERSON><PERSON><PERSON> ch<PERSON>h
Thu thập dữ liệu xe hơi từ bonbanh.com
"""

import argparse
import sys
import logging
from datetime import datetime

from brand_scraper import BrandScraper
from car_scraper import CarScraper
from database import CarDatabase
from price_analyzer import PriceAnalyzer
from config import *


class BonbanhMainScraper:
    """Class chính điều khiển toàn bộ quá trình scraping"""

    def __init__(self, use_mysql=True):
        self.setup_logging()
        self.brand_scraper = BrandScraper()
        self.car_scraper = CarScraper()
        self.database = CarDatabase(use_mysql=use_mysql)
        self.price_analyzer = PriceAnalyzer(use_mysql=use_mysql)

    def setup_logging(self):
        """Thiết lập logging"""
        logging.basicConfig(
            level=getattr(logging, LOG_LEVEL),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(LOG_FILE, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def scrape_brands_and_models(self):
        """Thu thập danh sách hãng xe và models"""
        self.logger.info("=== BẮT ĐẦU THU THẬP DANH SÁCH HÃNG XE VÀ MODELS ===")

        # Lấy danh sách brands
        brands = self.brand_scraper.get_all_brands()
        if not brands:
            self.logger.error("Không thể lấy danh sách hãng xe")
            return False

        # Lưu brands vào database
        for brand_slug, brand_data in brands.items():
            self.database.save_brand(brand_slug, brand_data)

        # Lấy models cho từng brand
        for brand_slug in brands:
            models = self.brand_scraper.get_models_for_brand(brand_slug)

            # Lưu models vào database
            for model_slug, model_data in models.items():
                self.database.save_model(brand_slug, model_slug, model_data)

        self.logger.info(f"Đã thu thập {len(brands)} hãng xe")
        return True

    def scrape_car_listings(self, brands_to_scrape=None, max_cars_per_brand=None):
        """Thu thập listings xe"""
        self.logger.info("=== BẮT ĐẦU THU THẬP LISTINGS XE ===")

        if brands_to_scrape is None:
            # Sử dụng priority brands nếu không chỉ định
            brands = self.brand_scraper.get_priority_brands()
        else:
            brands = {slug: self.brand_scraper.brands[slug]
                      for slug in brands_to_scrape
                      if slug in self.brand_scraper.brands}

        if not brands:
            self.logger.error("Không có brands để scrape")
            return False

        total_scraped = 0

        for brand_slug, brand_data in brands.items():
            self.logger.info(f"Đang scrape brand: {brand_slug}")

            # Lấy danh sách listings cho brand
            listings = self.car_scraper.get_all_listings_for_brand(
                brand_data['url'],
                max_pages=MAX_PAGES_PER_BRAND
            )

            if max_cars_per_brand:
                listings = listings[:max_cars_per_brand]

            brand_scraped = 0

            for listing_url in listings:
                try:
                    # Scrape chi tiết xe
                    car_data = self.car_scraper.scrape_car_detail(listing_url)

                    if car_data and car_data.get('listing_id'):
                        # Lưu vào database
                        if self.database.save_car(car_data):
                            brand_scraped += 1
                            total_scraped += 1

                            if brand_scraped % 10 == 0:
                                self.logger.info(f"Đã scrape {brand_scraped} xe cho {brand_slug}")

                except Exception as e:
                    self.logger.error(f"Lỗi khi scrape {listing_url}: {e}")
                    continue

            self.logger.info(f"Hoàn thành {brand_slug}: {brand_scraped} xe")

        self.logger.info(f"=== HOÀN THÀNH THU THẬP: {total_scraped} xe ===")
        return total_scraped > 0

    def analyze_prices(self):
        """Phân tích giá và tạo báo cáo"""
        self.logger.info("=== BẮT ĐẦU PHÂN TÍCH GIÁ ===")

        # Export dữ liệu
        self.database.export_to_csv()
        self.database.export_to_json()

        # Phân tích giá
        self.price_analyzer.export_price_analysis()

        # In thống kê
        car_count = self.database.get_car_count()
        self.logger.info(f"Tổng số xe trong database: {car_count}")

        return True

    def run_full_scrape(self, brands=None, max_cars_per_brand=None):
        """Chạy toàn bộ quá trình scraping"""
        start_time = datetime.now()
        self.logger.info(f"=== BẮT ĐẦU SCRAPING LÚC {start_time} ===")

        try:
            # 1. Thu thập brands và models
            if not self.scrape_brands_and_models():
                return False

            # 2. Thu thập car listings
            if not self.scrape_car_listings(brands, max_cars_per_brand):
                return False

            # 3. Phân tích giá
            self.analyze_prices()

            end_time = datetime.now()
            duration = end_time - start_time
            self.logger.info(f"=== HOÀN THÀNH SCRAPING SAU {duration} ===")

            return True

        except Exception as e:
            self.logger.error(f"Lỗi trong quá trình scraping: {e}")
            return False

    def run_test_scrape(self):
        """Chạy test scraping với dữ liệu nhỏ"""
        self.logger.info("=== CHẠY TEST SCRAPING ===")

        # Lấy danh sách brands trước
        if not self.brand_scraper.brands:
            self.brand_scraper.get_all_brands()

        # Lấy brand đầu tiên có sẵn để test
        available_brands = list(self.brand_scraper.brands.keys())
        if available_brands:
            test_brands = [available_brands[0]]  # Lấy brand đầu tiên
            self.logger.info(f"Test với brand: {test_brands[0]}")
        else:
            self.logger.error("Không tìm thấy brand nào để test")
            return False

        return self.run_full_scrape(brands=test_brands, max_cars_per_brand=5)


def main():
    """Hàm main"""
    parser = argparse.ArgumentParser(description='Bonbanh Car Scraper')
    parser.add_argument('--mode', choices=['full', 'test', 'analyze'],
                        default='test', help='Chế độ chạy')
    parser.add_argument('--brands', nargs='+',
                        help='Danh sách brands cần scrape')
    parser.add_argument('--max-cars', type=int,
                        help='Số xe tối đa mỗi brand')

    args = parser.parse_args()

    scraper = BonbanhMainScraper()

    if args.mode == 'test':
        success = scraper.run_test_scrape()
    elif args.mode == 'full':
        success = scraper.run_full_scrape(args.brands, args.max_cars)
    elif args.mode == 'analyze':
        success = scraper.analyze_prices()
    else:
        print("Chế độ không hợp lệ")
        return 1

    return 0 if success else 1


if __name__ == '__main__':
    sys.exit(main())
