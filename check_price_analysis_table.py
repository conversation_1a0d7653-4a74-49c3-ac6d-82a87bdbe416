#!/usr/bin/env python3
"""
Script kiểm tra bảng price_analysis trong MySQL
"""

import mysql.connector
import os
from dotenv import load_dotenv

load_dotenv()

def check_price_analysis_table():
    """Kiểm tra bảng price_analysis"""
    
    print("📊 KIỂM TRA BẢNG PRICE_ANALYSIS")
    print("=" * 60)
    
    try:
        mysql_config = {
            'host': os.getenv('DB_HOST', '127.0.0.1'),
            'port': int(os.getenv('DB_PORT', 3306)),
            'database': os.getenv('DB_DATABASE', 'xehoi_pro'),
            'user': os.getenv('DB_USERNAME', 'root'),
            'password': os.getenv('DB_PASSWORD', 'pwdpwd')
        }
        
        connection = mysql.connector.connect(**mysql_config)
        cursor = connection.cursor()
        
        # Check if table exists
        cursor.execute("""
            SELECT COUNT(*) 
            FROM information_schema.tables 
            WHERE table_schema = %s AND table_name = 'price_analysis'
        """, (mysql_config['database'],))
        
        table_exists = cursor.fetchone()[0] > 0
        
        if not table_exists:
            print("❌ Bảng price_analysis không tồn tại!")
            return False
        
        print("✅ Bảng price_analysis tồn tại")
        
        # Check total records
        cursor.execute("SELECT COUNT(*) FROM price_analysis")
        total_count = cursor.fetchone()[0]
        print(f"📊 Tổng số records: {total_count}")
        
        if total_count == 0:
            print("⚠️  Bảng price_analysis trống!")
            return False
        
        # Check by analysis type
        print("\n📋 PHÂN TÍCH THEO LOẠI:")
        cursor.execute("""
            SELECT analysis_type, COUNT(*) as count 
            FROM price_analysis 
            GROUP BY analysis_type
            ORDER BY analysis_type
        """)
        
        type_counts = cursor.fetchall()
        for analysis_type, count in type_counts:
            print(f"  - {analysis_type}: {count} records")
        
        # Brand analysis
        print("\n🏢 PHÂN TÍCH THEO BRAND:")
        cursor.execute("""
            SELECT brand, count, avg_price, median_price, min_price, max_price, std_price
            FROM price_analysis 
            WHERE analysis_type = 'brand'
            ORDER BY avg_price DESC
        """)
        
        brand_data = cursor.fetchall()
        for brand, count, avg_price, median_price, min_price, max_price, std_price in brand_data:
            print(f"  📁 {brand}:")
            print(f"     Số xe: {count}")
            print(f"     Giá TB: {avg_price:,} VND")
            print(f"     Giá trung vị: {median_price:,} VND")
            print(f"     Khoảng giá: {min_price:,} - {max_price:,} VND")
            print(f"     Độ lệch chuẩn: {std_price:,} VND")
            print()
        
        # Model analysis (top 10)
        print("🚗 PHÂN TÍCH THEO MODEL (Top 10):")
        cursor.execute("""
            SELECT brand, model, count, avg_price, median_price, min_price, max_price
            FROM price_analysis 
            WHERE analysis_type = 'model'
            ORDER BY count DESC, avg_price DESC
            LIMIT 10
        """)
        
        model_data = cursor.fetchall()
        for brand, model, count, avg_price, median_price, min_price, max_price in model_data:
            print(f"  🚙 {brand} {model}:")
            print(f"     Số xe: {count}")
            print(f"     Giá TB: {avg_price:,} VND")
            print(f"     Khoảng giá: {min_price:,} - {max_price:,} VND")
            print()
        
        # Year analysis (recent years)
        print("📅 PHÂN TÍCH THEO NĂM (Gần đây):")
        cursor.execute("""
            SELECT brand, model, year, count, avg_price, min_price, max_price
            FROM price_analysis 
            WHERE analysis_type = 'year' AND year >= 2020
            ORDER BY year DESC, avg_price DESC
            LIMIT 15
        """)
        
        year_data = cursor.fetchall()
        current_year = None
        for brand, model, year, count, avg_price, min_price, max_price in year_data:
            if year != current_year:
                print(f"\n  📆 Năm {year}:")
                current_year = year
            print(f"    - {brand} {model}: {count} xe, TB: {avg_price:,} VND")
        
        # Summary statistics
        print("\n" + "=" * 60)
        print("📈 THỐNG KÊ TỔNG QUAN:")
        
        cursor.execute("""
            SELECT 
                COUNT(DISTINCT brand) as total_brands,
                COUNT(DISTINCT CONCAT(brand, '-', model)) as total_models,
                COUNT(DISTINCT year) as total_years,
                SUM(count) as total_cars,
                AVG(avg_price) as overall_avg_price,
                MIN(min_price) as lowest_price,
                MAX(max_price) as highest_price
            FROM price_analysis
        """)
        
        stats = cursor.fetchone()
        total_brands, total_models, total_years, total_cars, overall_avg_price, lowest_price, highest_price = stats
        
        print(f"  📁 Tổng brands: {total_brands}")
        print(f"  🚗 Tổng models: {total_models}")
        print(f"  📅 Tổng năm: {total_years}")
        print(f"  🏷️  Tổng xe: {total_cars}")
        print(f"  💰 Giá trung bình tổng: {overall_avg_price:,.0f} VND")
        print(f"  📉 Giá thấp nhất: {lowest_price:,} VND")
        print(f"  📈 Giá cao nhất: {highest_price:,} VND")
        
        connection.close()
        
        print("\n✅ Bảng price_analysis hoạt động tốt!")
        return True
        
    except Exception as e:
        print(f"❌ Lỗi khi kiểm tra bảng price_analysis: {e}")
        return False

if __name__ == '__main__':
    check_price_analysis_table()
